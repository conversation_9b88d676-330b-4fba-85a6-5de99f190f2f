package com.facishare.crm.management.resource;

import com.facishare.crm.enums.ScheduleType;
import com.facishare.crm.management.service.PartnerChannelService;
import com.facishare.crm.management.service.PartnerManagementParamCheckService;
import com.facishare.crm.management.service.PrmManagementService;
import com.facishare.crm.model.PartnerChannelManage;
import com.facishare.crm.model.PrmManagementModel;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

/**
 * <AUTHOR>
 * @time 2023-09-22 16:07
 * @Description
 */
@Slf4j
@Component
@ServiceModule("partner_management")
public class ChannelManagementResource {
    @Resource
    private PartnerChannelService partnerChannelService;
    @Resource
    private PrmManagementService prmManagementService;
    @Resource
    private PartnerManagementParamCheckService partnerManagementParamCheckService;
    @Resource(name = "objectConverter")
    private Converter converter;

    @ServiceMethod("channel_save_notice_types")
    public PartnerChannelManage.Result saveNoticeTypes(ServiceContext context, PartnerChannelManage.NoticeConfigArg noticeConfig) {
        if (StringUtils.isBlank(noticeConfig.getConfigId())) {
            throw new ValidateException(I18N.text(SFA_PARTNER_MANAGEMENT_CONFIG_DATA_ID_NOT_EMPTY));
        }
        return partnerChannelService.saveNoticeTypes(context.getUser(), noticeConfig.getConfigId(), noticeConfig.getNoticeTypes());
    }

    @ServiceMethod("channel_init_create_partner_config_data")
    public Boolean createPartnerConfigData(ServiceContext context, PartnerChannelManage.DefaultRoleArg roleArg) {
        return partnerChannelService.createPartnerConfigData(context.getUser(), roleArg.getRoleCodes()) != null;
    }


    @ServiceMethod("channel_config_info")
    public PartnerChannelManage.Result getConfigInfo(ServiceContext context) {
        return partnerChannelService.getConfigInfo(context.getUser());
    }

    /**
     * 获取配置信息
     *
     * @param context
     * @return
     */
    @ServiceMethod("prm_notice_config")
    public PrmManagementModel.NoticeConfigResult prmConfigInfo(ServiceContext context) {
        return prmManagementService.getConfigInfo(context.getUser());
    }

    /**
     * 创建配置信息
     *
     * @param context
     * @param arg
     * @return
     */
    @ServiceMethod("prm_save_notice_config")
    public PrmManagementModel.NoticeConfigResult savePrmNoticeInfo(ServiceContext context, PrmManagementModel.NoticeConfigArg arg) {
        if (arg == null) {
            return prmManagementService.getConfigInfo(context.getUser());
        }
        partnerManagementParamCheckService.initDefault(arg);
        partnerManagementParamCheckService.checkPrmNoticeConfigType(arg);
        partnerManagementParamCheckService.checkPrmNoticeType(arg);
        partnerManagementParamCheckService.checkPrmSmsType(arg);
        return prmManagementService.saveNoticeInfo(context.getUser(), arg);
    }


    // ============================== 渠道准入 begin ===============================
    // ｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜
    // ============================== 首页 begin ==================================

    /**
     * 保存结果通知配置
     *
     * @param context      上下文
     * @param noticeConfig 参数
     * @return 结果
     */
    @ServiceMethod("channel_save_notice_info")
    public PartnerChannelManage.Result saveNoticeConfigInfo(ServiceContext context, PartnerChannelManage.NoticeConfigArg noticeConfig) {
        if (StringUtils.isBlank(noticeConfig.getConfigId())) {
            throw new ValidateException(I18N.text(SFA_PARTNER_MANAGEMENT_CONFIG_DATA_ID_NOT_EMPTY));
        }
        return partnerChannelService.saveNoticeConfig(context.getUser(), noticeConfig);
    }

    /**
     * 获取应用配置界面布局
     *
     * @return 返回结果
     */
    @ServiceMethod("app_config_layout")
    public PartnerChannelManage.AppConfigLayoutResult queryAppConfigLayout(ServiceContext context) {
        ArrayList<PartnerChannelManage.AppConfigLayout> appConfigLayouts = partnerChannelService.queryAppConfigLayout(context.getUser());
        return PartnerChannelManage.AppConfigLayoutResult.builder().appConfigLayouts(appConfigLayouts).build();
    }

    /**
     * 保存渠道准入管理配置
     *
     * @param admissionConfig 管理配置
     * @return 返回结果
     */
    @ServiceMethod("save_channel_admission_config")
    public PartnerChannelManage.AdmissionConfig saveChannelAdmissionConfig(ServiceContext context, PartnerChannelManage.AdmissionConfig admissionConfig) {
        partnerManagementParamCheckService.checkChannelAdmissionConfig(admissionConfig);
        return partnerChannelService.saveChannelAdmissionConfig(context.getUser(), admissionConfig);
    }

    /**
     * 查询管理配置
     *
     * @param context 上下文
     * @return 管理配置
     */
    @ServiceMethod("fetch_channel_admission_config")
    public PartnerChannelManage.AdmissionConfig fetchChannelAdmissionConfig(ServiceContext context) {
        return partnerChannelService.fetchChannelAdmissionConfig(context.getUser());
    }

    // ============================== 首页 end ====================================
    // ｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜
    // ============================== 注册审核 begin ===============================

    /**
     * 查询注册审核通知界面数据
     *
     * @param context 上下文
     * @return 注册审核通知界面数据
     */
    @ServiceMethod("channel_query_approval_notice")
    public PartnerChannelManage.RegistrationApprovalResult queryApprovalNotice(ServiceContext context) {
        List<PartnerChannelManage.ApprovalNotice> approvalNotices = partnerChannelService.queryApprovalNotice(context.getUser());
        return PartnerChannelManage.RegistrationApprovalResult.builder().approvalNotices(approvalNotices).build();

    }

    // 查询企业账号开通配置
    @ServiceMethod("channel_query_activation_settings")
    public PartnerChannelManage.RegistrationApprovalResult queryActivationSettings(ServiceContext context) {
        return partnerChannelService.queryActivationSettings(context.getUser());
    }

    // 保存企业账号开通配置
    @ServiceMethod("channel_save_activation_settings")
    public PartnerChannelManage.RegistrationApprovalResult saveActivationSettings(ServiceContext context, PartnerChannelManage.EnterpriseActivationSettingArg activationSettingArg) {
        partnerManagementParamCheckService.checkEnterpriseActivationSettings(activationSettingArg);
        List<PartnerChannelManage.EnterpriseActivationSetting> enterpriseActivationSettings = partnerChannelService.saveActivationSettings(context.getUser(), activationSettingArg.getEnterpriseActivationSettings());
        PartnerChannelManage.CustomText customText = partnerChannelService.saveRegisterCustomText(context.getUser(), activationSettingArg.getCustomText());
        return PartnerChannelManage.RegistrationApprovalResult
                .builder()
                .conditionType(activationSettingArg.getConditionType())
                .customText(customText)
                .enterpriseActivationSettings(enterpriseActivationSettings)
                .build();
    }

    /**
     * 指定「开通基础账号后的默认角色」
     *
     * @param context        上下文
     * @param defaultRoleArg 参数
     * @return 返回值
     */
    @ServiceMethod("channel_specify_default_role")
    public PartnerChannelManage.SpecifyRoleResult specifyDefaultRole(ServiceContext context, PartnerChannelManage.DefaultRoleArg defaultRoleArg) {
        List<String> roleCodes = defaultRoleArg.getRoleCodes();
        if (CollectionUtils.isEmpty(roleCodes)) {
            throw new ValidateException(I18N.text(SFA_PARTNER_MANAGEMENT_ROLE_NOT_EMPTY));
        }
        String configId = defaultRoleArg.getConfigId();
        if (StringUtils.isBlank(configId)) {
            throw new ValidateException(I18N.text(SFA_PARTNER_MANAGEMENT_CONFIG_DATA_ID_NOT_EMPTY));
        }
        List<String> existsRoleCodes = partnerChannelService.getAppRoleList(context.getUser()).stream().map(PartnerChannelManage.DefaultRoleResult::getRoleCode).collect(Collectors.toList());
        roleCodes.stream().filter(role -> !existsRoleCodes.contains(role)).findAny().ifPresent(role -> {
            throw new ValidateException(I18N.text(SFA_PARTNER_MANAGEMENT_ROLE_NOT_EXISTS));
        });
        boolean specified = partnerChannelService.specifyDefaultRole(context.getUser(), configId, roleCodes);
        if (!specified) {
            throw new ValidateException(I18N.text(SFA_PARTNER_MANAGEMENT_SPECIFY_ROLE_FAILED));
        }
        return PartnerChannelManage.SpecifyRoleResult.builder().configId(configId).roleCodes(roleCodes).build();
    }
    // ============================== 注册审核 end =================================
    // ｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜
    // ============================== 浏览规定 begin ===============================

    /**
     * 查询浏览规定数据
     *
     * @param context 上下文
     * @return 浏览规定数据
     */
    @ServiceMethod("channel_query_provision_scheme")
    public PartnerChannelManage.ProvisionSchemeResult queryProvisionScheme(ServiceContext context) {
        List<PartnerChannelManage.ProvisionScheme> provisionScheme = partnerChannelService.queryProvisionScheme(context.getUser());
        return PartnerChannelManage.ProvisionSchemeResult.builder().provisionScheme(provisionScheme).build();
    }

    /**
     * 保存浏览规定数据
     *
     * @param context            上下文
     * @param provisionSchemeArg 浏览规定参数
     * @return 浏览规则数据
     */
    @ServiceMethod("channel_save_provision_scheme")
    public PartnerChannelManage.ProvisionSchemeResult saveProvisionScheme(ServiceContext context, PartnerChannelManage.ProvisionSchemeArg provisionSchemeArg) {
        partnerManagementParamCheckService.checkProvisionScheme(context.getUser(), provisionSchemeArg);
        List<PartnerChannelManage.ProvisionScheme> provisionScheme = partnerChannelService.saveProvisionScheme(context.getUser(), provisionSchemeArg);
        return PartnerChannelManage.ProvisionSchemeResult.builder().provisionScheme(provisionScheme).build();
    }
    // ============================== 浏览规定 end =================================
    // ｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜
    // ============================== 资质完善 begin ===============================

    /**
     * 查询资质完善方案
     *
     * @param context 上下文
     * @return 资质完善方案结果
     */
    @ServiceMethod("channel_query_qualification_scheme")
    public PartnerChannelManage.QualificationSchemeResult queryQualificationScheme(ServiceContext context) {
        List<PartnerChannelManage.QualificationScheme> qualificationScheme = partnerChannelService.queryQualificationScheme(context.getUser());
        return PartnerChannelManage.QualificationSchemeResult.builder().qualificationScheme(qualificationScheme).build();
    }

    /**
     * 保存资质完善方案
     *
     * @param context                上下文
     * @param qualificationSchemeArg 资质完善方案参数
     * @return 资质完善方案结果
     */
    @ServiceMethod("channel_save_qualification_scheme")
    public PartnerChannelManage.QualificationSchemeResult saveQualificationScheme(ServiceContext context, PartnerChannelManage.QualificationSchemeArg qualificationSchemeArg) {
        partnerManagementParamCheckService.checkQualificationSchemeArg(qualificationSchemeArg);
        List<PartnerChannelManage.QualificationScheme> qualificationScheme = partnerChannelService.saveQualificationScheme(context.getUser(), qualificationSchemeArg.getQualificationScheme());
        return PartnerChannelManage.QualificationSchemeResult.builder().qualificationScheme(qualificationScheme).build();
    }
    // ============================== 资质完善 end =================================
    // ｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜
    // ============================== 协议签约 begin ===============================

    /**
     * 查询签约方案
     *
     * @param context 上下文
     * @return 签约方案结果
     */
    @ServiceMethod("channel_query_sign_scheme")
    public PartnerChannelManage.SimpleSignSchemeResult querySignScheme(ServiceContext context) {
        List<PartnerChannelManage.SimpleSignScheme> simpleSignSchemes = partnerChannelService.querySimpleSignSchemeList(context.getUser());
        return PartnerChannelManage.SimpleSignSchemeResult.builder().signSchemes(simpleSignSchemes).build();

    }

    /**
     * 查询签约方案明细
     *
     * @param context         上下文
     * @param opSignSchemeArg 参数
     * @return 签约方案明细
     */
    @ServiceMethod("fetch_sign_scheme_detail")
    public PartnerChannelManage.SignSchemeDetail fetchSignSchemeDetail(ServiceContext context, PartnerChannelManage.OpSignSchemeArg opSignSchemeArg) {
        PartnerChannelManage.SignScheme signScheme = partnerChannelService.fetchSignScheme(context.getUser(), opSignSchemeArg);
        return PartnerChannelManage.SignSchemeDetail.builder().signScheme(signScheme).build();

    }

    /**
     * 新建签约方案
     *
     * @param context       上下文
     * @param signSchemeArg 参数
     * @return 签约方案保存结果
     */
    @ServiceMethod("channel_add_sign_scheme")
    public PartnerChannelManage.SignSchemeResponse<PartnerChannelManage.SignScheme> addSignScheme(ServiceContext context, PartnerChannelManage.SignSchemeArg signSchemeArg) {
        PartnerChannelManage.SignScheme signSchemeResult = null;
        PartnerChannelManage.SignScheme signScheme = signSchemeArg.getSignScheme();
        ScheduleType signType = ScheduleType.from(signScheme.getScheduleType());
        if (ScheduleType.ONE_TIME == signType) {
            PartnerChannelManage.OneTimeSignScheme oneTimeSignScheme = converter.convertDTO(signScheme, PartnerChannelManage.OneTimeSignScheme.class);
            signSchemeResult = partnerChannelService.addOneTimeSignScheme(context.getUser(), oneTimeSignScheme);
        } else if (ScheduleType.CYCLE == signType) {
            PartnerChannelManage.CycleSignScheme cycleSignScheme = converter.convertDTO(signScheme, PartnerChannelManage.CycleSignScheme.class);
            signSchemeResult = partnerChannelService.addCycleDataSignScheme(context.getUser(), cycleSignScheme);
        } else if (ScheduleType.FIXED_DATE == signType) {
            PartnerChannelManage.FixedDateSignScheme fixedDateSignScheme = converter.convertDTO(signScheme, PartnerChannelManage.FixedDateSignScheme.class);
            signSchemeResult = partnerChannelService.addFixedDataSignScheme(context.getUser(), fixedDateSignScheme);
        }
        return PartnerChannelManage.SignSchemeResponse.<PartnerChannelManage.SignScheme>builder().data(signSchemeResult).build();
    }

    /**
     * 更新签约方案
     *
     * @param context       上下文
     * @param signSchemeArg 参数
     * @return 签约方案保存结果
     */
    @ServiceMethod("channel_edit_sign_scheme")
    public PartnerChannelManage.SignSchemeResponse<PartnerChannelManage.SignScheme> signSchemeEdit(ServiceContext context, PartnerChannelManage.SignSchemeArg signSchemeArg) {
//        partnerManagementParamCheckService.checkSignSchemeArg(context.getUser(), signSchemeArg);
        PartnerChannelManage.SignScheme signScheme = partnerChannelService.signSchemeEdit(context.getUser(), signSchemeArg.getSignScheme());
        return PartnerChannelManage.SignSchemeResponse.<PartnerChannelManage.SignScheme>builder().data(signScheme).build();
    }

    /**
     * 删除签约方案
     *
     * @param context         上下文
     * @param opSignSchemeArg 参数
     * @return 删除签约方案结果
     */
    @ServiceMethod("channel_delete_sign_scheme")
    public PartnerChannelManage.DeleteSignSchemeResult deleteSignScheme(ServiceContext context, PartnerChannelManage.OpSignSchemeArg opSignSchemeArg) {
        partnerManagementParamCheckService.checkDeleteSignSchemeArg(context.getUser(), opSignSchemeArg.getSignSchemeId());
        partnerChannelService.deleteSignScheme(context.getUser(), opSignSchemeArg.getSignSchemeId());
        partnerChannelService.sortSignScheme(context.getUser());
        return PartnerChannelManage.DeleteSignSchemeResult.builder().deleteSuccess(true).build();
    }

    /**
     * 启用/停用签约方案
     *
     * @param context             上下文
     * @param toggleSignSchemeArg 参数
     * @return 启用/停用签约方案结果
     */
    @ServiceMethod("toggle_scheme_status")
    public PartnerChannelManage.PushSignSchemeResult toggleStatus(ServiceContext context, PartnerChannelManage.PushSignSchemeArg toggleSignSchemeArg) {
        partnerManagementParamCheckService.checkPushSignSchemeArg(context.getUser(), toggleSignSchemeArg.getSignSchemeId());
        Boolean pushed = partnerChannelService.toggleSchemeStatus(context.getUser(), toggleSignSchemeArg);
        return PartnerChannelManage.PushSignSchemeResult.builder().toggleSuccess(pushed).build();
    }

    /**
     * 解锁启用/停用，仅内部使用
     *
     * @param context 上下文
     * @return 解锁启用/停用结果
     */
    @ServiceMethod("unLock_toggle_status")
    public PartnerChannelManage.PushSignSchemeResult unLockToggleStatus(ServiceContext context) {
        Boolean success = partnerChannelService.unLockToggleStatus(context.getUser());
        return PartnerChannelManage.PushSignSchemeResult.builder().toggleSuccess(success).build();
    }

    /**
     * 保存签约方案自定义提示内容
     *
     * @param context        上下文
     * @param customTextData 签约方案自定义提示内容
     * @return 保存的签约方案自定义提示内容
     */
    @ServiceMethod("save_custom_sign_text")
    public PartnerChannelManage.CustomTextData saveCustomSignText(ServiceContext context, PartnerChannelManage.CustomTextData customTextData) {
        return partnerChannelService.saveCustomSignText(context.getUser(), customTextData);
    }

    /**
     * 查询签约方案自定义提示内容
     *
     * @param context 上下文
     * @return 签约方案自定义提示内容
     */
    @ServiceMethod("fetch_custom_sign_text")
    public PartnerChannelManage.CustomTextData fetchCustomSignText(ServiceContext context) {
        return partnerChannelService.fetchCustomSignText(context.getUser());
    }
    // ============================== 协议签约 end =================================
    // ｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜
    // =============================== 渠道准入 Rest 接口 begin ===============================

    /**
     * 创建合作伙伴联系人
     * caller：fs-crm-task-sfa
     *
     * @param context   上下文
     * @param partnerId 合作伙伴id
     * @return 创建结果
     */
    @ServiceMethod("channel_create_contact_by_partner")
    public PartnerChannelManage.CreateDataResult createContactByPartner(ServiceContext context, String partnerId) {
        PartnerChannelManage.CreateDataResult rst = new PartnerChannelManage.CreateDataResult();
        if (StringUtils.isBlank(partnerId)) {
            rst.setError("partner data id is null");
            return rst;
        }
        try {
            BaseObjectSaveAction.Result contactData = partnerChannelService.createContactByPartner(context.getUser(), partnerId);
            if (contactData != null && contactData.getObjectData() != null) {
                rst.setDataId(contactData.getObjectData().getId());
            }
        } catch (AppBusinessException appEx) {
            log.warn("createPartnerContactByPartner ValidateException, tenant:{}", context.getTenantId(), appEx);
            rst.setError(appEx.getMessage());
        } catch (Exception ex) {
            rst.setError("system error");
            log.warn("createPartnerContactByPartner Error, tenant:{}", context.getTenantId(), ex);
        }
        return rst;
    }

    /**
     * 初始化映射规则
     *
     * @param context 上下文
     * @return 初始化结果
     */
    @ServiceMethod("channel_init_mapping")
    public Boolean initPartnerChannelMapping(ServiceContext context) {
        return partnerChannelService.initPartnerChannelMapping(context.getUser());
    }
    // =============================== 渠道准入 Rest 接口 end =================================
    // ｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜
    // =============================== 渠道准入通用逻辑 begin ==================================

    /**
     * 短信状态查询
     *
     * @param context 上下文
     * @return 短信状态结果
     */
    @ServiceMethod("channel_provisioning_sms")
    public Boolean canProvisionSms(ServiceContext context) {
        return partnerChannelService.canProvisionSms(context.getUser());
    }

    /**
     * 保存 APL
     *
     * @param context 上下文
     * @param aplArg  APL 参数
     * @return 保存结果
     */
    @ServiceMethod("channel_save_notice_apl")
    public PartnerChannelManage.NoticeInstanceResult saveNoticeApl(ServiceContext context, PartnerChannelManage.NoticeAplArg aplArg) {
        partnerManagementParamCheckService.checkAplArg(context.getUser(), aplArg);
        return partnerChannelService.saveNoticeApl(context.getUser(), aplArg);
    }

    /**
     * 查询短信可配置的电话字段描述
     *
     * @param context 上下文
     * @return 可配置的电话字段描述
     */
    @ServiceMethod("channel_phone_field_describe")
    public IObjectDescribe getPhoneFieldDescribe(ServiceContext context) {
        return partnerChannelService.getPhoneFieldDescribe(context.getUser());
    }

    /**
     * 查询可配置的邮件字段描述
     *
     * @param context 上下文
     * @return 邮件字段描述
     */
    @ServiceMethod("channel_email_field_describe")
    public IObjectDescribe getEmailFieldDescribe(ServiceContext context) {
        return partnerChannelService.getEmailFieldDescribe(context.getUser());
    }

    /**
     * 保存短信提醒配置
     *
     * @param context 上下文
     * @param smsArg  短信参数
     * @return 保存结果
     */
    @ServiceMethod("channel_save_sms_instance")
    public PartnerChannelManage.NoticeInstanceResult saveSmsInstance(ServiceContext context, PartnerChannelManage.ApprovalNoticeSmsInstanceArg smsArg) {
        partnerManagementParamCheckService.checkApprovalNoticeSmsInstance(context.getUser(), smsArg);
        return partnerChannelService.saveSmsInstance(context.getUser(), smsArg);
    }

    /**
     * 保存邮件提醒配置
     *
     * @param context  上下文
     * @param emailArg 邮件参数
     * @return 保存结果
     */
    @ServiceMethod("channel_save_email_instance")
    public PartnerChannelManage.NoticeInstanceResult saveEmailInstance(ServiceContext context, PartnerChannelManage.ApprovalNoticeEmailInstanceArg emailArg) {
        partnerManagementParamCheckService.checkApprovalNoticeEmailInstance(context.getUser(), emailArg);
        return partnerChannelService.saveEmailInstance(context.getUser(), emailArg);
    }

    /**
     * 查询具体的通知实例，比如查短信、查邮件
     *
     * @param context     上下文
     * @param instanceArg 参数
     * @return 查询结果
     */
    @ServiceMethod("channel_query_notice_instance")
    public PartnerChannelManage.NoticeInstanceResult queryNoticeInstance(ServiceContext context, PartnerChannelManage.NoticeInstanceArg instanceArg) {
        return partnerChannelService.queryNoticeInstance(context.getUser(), instanceArg);
    }

    /**
     * 切换短信、邮件开关状态
     *
     * @param context    上下文
     * @param switchItem 切换开关状态参数
     * @return 切换结果
     */
    @ServiceMethod("channel_toggle_switch_status")
    public PartnerChannelManage.ApprovalNotice toggleSwitchStatus(ServiceContext context, PartnerChannelManage.SwitchItem switchItem) {
        partnerManagementParamCheckService.checkSwitchScopeArg(context.getUser(), switchItem);
        return partnerChannelService.toggleSwitchStatus(context.getUser(), switchItem);
    }

    /**
     * 查询应用角色列表
     *
     * @param context 上下文
     * @return 角色列表
     */
    @ServiceMethod("channel_role_list")
    public PartnerChannelManage.RoleResult getAppRoleList(ServiceContext context) {
        List<PartnerChannelManage.DefaultRoleResult> appRoleList = partnerChannelService.getAppRoleList(context.getUser());
        return PartnerChannelManage.RoleResult.builder().roles(appRoleList).build();
    }
    // =============================== 渠道准入通用逻辑 end ====================================
    // ｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜｜
    // ============================== 渠道准入 end ===========================================
}
