package com.facishare.crm.management.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.enums.*;
import com.facishare.crm.management.service.access.*;
import com.facishare.crm.management.service.config.BizConfigKey;
import com.facishare.crm.management.service.model.ApprovalNoticeModel;
import com.facishare.crm.management.service.model.EnterpriseActivationSettingModel;
import com.facishare.crm.management.service.model.LayoutSchemeModel;
import com.facishare.crm.management.service.model.SwitchEventMessage;
import com.facishare.crm.model.*;
import com.facishare.crm.platform.async.executor.AsyncBootstrap;
import com.facishare.crm.platform.converter.Converter;
import com.facishare.crm.platform.utils.DiffUtils;
import com.facishare.crm.platform.utils.ObjectDataUtils;
import com.facishare.crm.sfa.predefine.SFAPreDefine;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.predefine.service.model.PartnerAgreementDetailModel;
import com.facishare.crm.sfa.prm.api.enhancer.DescribeEnhancer;
import com.facishare.crm.sfa.prm.platform.enums.TimeUnit;
import com.facishare.crm.sfa.prm.platform.utils.I18NUtils;
import com.facishare.crm.sfa.task.AsyncTaskProducer;
import com.facishare.crm.sfa.utilities.common.UseRangeFieldDataRender;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.ChannelAppLayoutConfig;
import com.facishare.crm.sfa.utilities.util.JsonUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.SmsServiceProxy;
import com.facishare.paas.appframework.common.service.dto.CheckSmsStatus;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.rule.pojo.RuleGroupPojo;
import com.fxiaoke.enterpriserelation2.arg.ListAppOuterRolesByAppIdArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.data.RoleInfoData;
import com.fxiaoke.enterpriserelation2.service.AppOuterRoleService;
import com.google.common.collect.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.crm.constants.PrmI18NConstants.*;
import static com.facishare.crm.enums.EnterpriseTypeEnums.NON_CRM;
import static com.facishare.crm.enums.NoticeInstanceCategoryEnums.NON_PASS;
import static com.facishare.crm.enums.NoticeInstanceCategoryEnums.PASS;
import static com.facishare.crm.enums.NotifyViaEnums.EMAIL;
import static com.facishare.crm.enums.NotifyViaEnums.SMS;
import static com.facishare.crm.enums.RecycleModeEnums.FIXED_DAY;
import static com.facishare.crm.management.service.model.EnterpriseActivationSettingModel.DEFAULT_ROLES;
import static com.facishare.crm.model.PartnerChannelConfigModel.*;
import static com.facishare.crm.model.ProvisionSchemeModel.*;
import static com.facishare.crm.sfa.service.ChannelService.REGISTER_CUSTOM_TEXT;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

/**
 * <AUTHOR>
 * @time 2023-09-25 18:12
 * @Description
 */
@Slf4j
@Service
public class PartnerChannelService {
    @Resource
    private PartnerChannelConfigAccess channelConfigAccess;
    @Resource
    private AppOuterRoleService appOuterRoleService;
    @Resource
    private PartnerOpenService partnerOpenService;
    @Resource
    private ConfigService configService;
    @Resource
    private DescribeLogicService describeLogicService;
    @Resource
    private SmsServiceProxy smsServiceProxy;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private ObjectMappingService objectMappingService;
    @Resource
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Resource
    private ActivationSettingAccess activationSettingAccess;
    @Resource
    private PrmEmailAccess prmEmailAccess;
    @Resource
    private PrmSmsAccess prmSmsAccess;
    @Resource
    private ApprovalNoticeAccess approvalNoticeAccess;
    @Resource
    private ProvisionSchemeAccess provisionSchemeAccess;
    @Resource
    private SignSchemeAccess signSchemeAccess;
    @Resource
    private LayoutSchemeAccess layoutSchemeAccess;
    @Resource
    private PaasRuleEngineService paasRuleEngineService;
    @Resource
    private ExpireReminderTypeAccess expireReminderTypeAccess;
    @Resource
    private ExpireReminderPersonAccess expireReminderPersonAccess;
    @Resource(name = "objectConverter")
    private Converter converter;
    @Resource
    private ChannelAppLayoutConfig channelAppLayoutConfig;
    @Resource
    private AsyncTaskProducer asyncTaskProducer;
    @Resource
    private DescribeEnhancer describeEnhancer;

    private static final Set<String> admissionConfigFields = ImmutableSet.of("channelMode", "applyToApp", "relatedObjectApiName");
    private String CHANGE_AGREEMENT_DETAIL_STATUS = "change_agreement_detail_status";

    public boolean specifyDefaultRole(User user, String configId, List<String> roleCodes) {
        ImmutableMap<String, Object> roleMapping = ImmutableMap.of(DEFAULT_ROLES, roleCodes);
        return channelConfigAccess.updateField(user, configId, roleMapping);
    }

    public PartnerChannelManage.Result getConfigInfo(User user) {
        IObjectData channelConfigData = channelConfigAccess.findChannelConfig(user);
        if (channelConfigData == null) {
            channelConfigData = createPartnerConfigData(user, Lists.newArrayList());
        }
        removeNotExistsRoles(user, channelConfigData);
        removeNotExistsPhoneFields(user, channelConfigData);
        PartnerChannelManage.ContentParamMapping contentParamMapping = getParamMapping(user);
        return buildResult(channelConfigData, contentParamMapping);
    }

    private void removeNotExistsPhoneFields(User user, IObjectData channelConfigData) {
        List<String> smsPhones = channelConfigData.get(PartnerChannelConfigModel.SMS_PHONES, List.class, Lists.newArrayList());
        List<String> smsPhoneFields = Lists.newArrayList();
        smsPhones.forEach(smsPhone -> smsPhoneFields.add(smsPhone.replace(PHONE_FIELD_PREFIX, "")));

        IObjectDescribe partnerDescribe = describeLogicService.findObject(user.getTenantId(), SFAPreDefine.Partner.getApiName());
        Set<String> describeFields = partnerDescribe.getFieldDescribes().stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());

        List<String> newPhones = Lists.newArrayList();
        AtomicBoolean updateField = new AtomicBoolean(false);
        smsPhoneFields.forEach(field -> {
            if (!describeFields.contains(field)) {
                updateField.set(true);
                return;
            }
            newPhones.add(PHONE_FIELD_PREFIX + field);
        });

        channelConfigData.set(PartnerChannelConfigModel.SMS_PHONES, newPhones);
        if (updateField.get()) {
            asyncUpdatePhones(user, channelConfigData.getId(), newPhones);
        }
    }

    private void asyncUpdatePhones(User user, String id, List<String> newPhones) {
        AsyncBootstrap.runAsyncTask(() -> {
            IObjectData data = metaDataFindServiceExt.findObjectData(user, id, API_NAME);
            if (data == null) {
                return;
            }
            data.set(SMS_PHONES, newPhones);
            metaDataFindServiceExt.bulkUpdateByFields(user, Lists.newArrayList(data), Lists.newArrayList(SMS_PHONES));
        });
    }

    private void removeNotExistsRoles(User user, IObjectData channelConfigData) {
        AtomicBoolean updateRole = new AtomicBoolean(false);
        List<String> defaultRoles = channelConfigData.get(PartnerChannelConfigModel.DEFAULT_ROLES, List.class, Lists.newArrayList());
        List<String> appRoleIds = getAppRoleList(user).stream().map(PartnerChannelManage.DefaultRoleResult::getRoleCode).collect(Collectors.toList());
        List<String> newRoleIds = Lists.newArrayList();
        defaultRoles.forEach(defaultRole -> {
            if (!appRoleIds.contains(defaultRole)) {
                updateRole.set(true);
                return;
            }
            newRoleIds.add(defaultRole);
        });
        channelConfigData.set(PartnerChannelConfigModel.DEFAULT_ROLES, newRoleIds);
        if (updateRole.get()) {
            asyncSpecifyDefaultRole(user, channelConfigData.getId(), newRoleIds);
        }
    }

    private void asyncSpecifyDefaultRole(User user, String configId, List<String> newRoleIds) {
        AsyncBootstrap.runAsyncTask(() -> specifyDefaultRole(user, configId, newRoleIds));
    }

    private PartnerChannelManage.ContentParamMapping getParamMapping(User user) {
        PartnerChannelManage.ContentParamMapping contentParamMapping = new PartnerChannelManage.ContentParamMapping();
        contentParamMapping.setPassParam(Lists.newArrayList());
        contentParamMapping.setFailParam(Lists.newArrayList());
        String tenantConfig = configService.findTenantConfig(user, PARTNER_CHANNEL_PARAM_MAPPING);
        if (StringUtils.isBlank(tenantConfig)) {
            return contentParamMapping;
        }
        try {
            contentParamMapping = JSON.parseObject(tenantConfig, PartnerChannelManage.ContentParamMapping.class);
        } catch (Exception e) {
            log.warn("json param mapping error， tenant：{}, json:{}", user.getTenantId(), tenantConfig, e);
        }
        return contentParamMapping;
    }

    private PartnerChannelManage.SmsContent buildTemplateContent(String smsTemplateId, String smsContent, List<PartnerChannelManage.ContentParam> paramMapping) {
        PartnerChannelManage.SmsContent sms = new PartnerChannelManage.SmsContent();
        sms.setTemplateId(smsTemplateId);
        sms.setContent(smsContent);
        sms.setSmsContentParam(paramMapping);
        return sms;
    }

    private PartnerChannelManage.Result buildResult(IObjectData objectData, PartnerChannelManage.ContentParamMapping contentParamMapping) {
        if (objectData == null) {
            return PartnerChannelManage.Result.builder().build();
        }
        String smsTemplatePassId = objectData.get(SMS_TEMPLATE_ID, String.class, "");
        String smsContent = objectData.get(SMS_CONTENT, String.class, "");
        PartnerChannelManage.SmsContent smsPass = buildTemplateContent(smsTemplatePassId, smsContent, contentParamMapping.getPassParam());

        String smsTemplateFailId = objectData.get(SMS_TEMPLATE_FAIL_ID, String.class, "");
        String smsFailContent = objectData.get(SMS_FAIL_CONTENT, String.class, "");
        PartnerChannelManage.SmsContent smsFail = buildTemplateContent(smsTemplateFailId, smsFailContent, contentParamMapping.getFailParam());
        List<String> smsPhones = objectData.get(PartnerChannelConfigModel.SMS_PHONES, List.class, Lists.newArrayList());
        List<String> smsPhoneFields = Lists.newArrayList();
        smsPhones.forEach(smsPhone -> smsPhoneFields.add(smsPhone.replace(PHONE_FIELD_PREFIX, "")));
        return PartnerChannelManage.Result.builder()
                .configId(objectData.getId())
                .defaultRoles(objectData.get(DEFAULT_ROLES, List.class, Lists.newArrayList()))
                .smsPhones(smsPhoneFields)
                .noticeTypes(objectData.get(NOTICE_TYPES, List.class, Lists.newArrayList()))
                .smsPassContent(smsPass)
                .smsFailContent(smsFail)
                .build();
    }

    public PartnerChannelManage.Result saveNoticeConfig(User user, PartnerChannelManage.NoticeConfigArg noticeConfig) {
        String configId = noticeConfig.getConfigId();
        IObjectData channelConfigData = channelConfigAccess.findChannelConfig(user, configId);
        if (channelConfigData == null) {
            throw new ValidateException(I18N.text(SFA_PARTNER_MANAGEMENT_DO_NOT_FIND_CONFIG_DATA));
        }
        List<String> noticeTypes = noticeConfig.getNoticeTypes();
        Optional.ofNullable(noticeTypes).orElse(Lists.newArrayList())
                .forEach(noticeType -> fillNoticeConfigByType(noticeType, noticeConfig, channelConfigData));
        IObjectData objectData = channelConfigAccess.updateObjectDataByFields(user,
                channelConfigData, Lists.newArrayList(SMS_PHONES, SMS_FAIL_CONTENT, SMS_TEMPLATE_FAIL_ID, SMS_CONTENT, SMS_TEMPLATE_ID));
        PartnerChannelManage.ContentParamMapping contentParamMapping = upsertParamMapping(user, noticeConfig);
        return buildResult(objectData, contentParamMapping);
    }

    private PartnerChannelManage.ContentParamMapping upsertParamMapping(User user, PartnerChannelManage.NoticeConfigArg noticeConfig) {
        List<PartnerChannelManage.ContentParam> failParamMapping = buildParamMapping(noticeConfig.getSmsFailContent());
        List<PartnerChannelManage.ContentParam> passParamMapping = buildParamMapping(noticeConfig.getSmsPassContent());
        PartnerChannelManage.ContentParamMapping contentParamMapping = new PartnerChannelManage.ContentParamMapping();
        contentParamMapping.setFailParam(failParamMapping);
        contentParamMapping.setPassParam(passParamMapping);
        configService.upsertTenantConfig(user, PARTNER_CHANNEL_PARAM_MAPPING, JsonUtil.toJsonWithNullValues(contentParamMapping), ConfigValueType.JSON);
        return contentParamMapping;
    }

    private List<PartnerChannelManage.ContentParam> buildParamMapping(PartnerChannelManage.SmsContent smsFailContent) {
        String templateId = smsFailContent.getTemplateId();
        if (StringUtils.isBlank(templateId)) {
            return Lists.newArrayList();
        }
        return smsFailContent.getSmsContentParam();
    }

    public IObjectData createPartnerConfigData(User user, List<String> roles) {
        IObjectData channelConfig = channelConfigAccess.findChannelConfig(user);
        if (channelConfig != null) {
            log.warn("createPartnerConfigData but config exists, tenant:{}, roles:{}", user.getTenantId(), roles);
            return null;
        }
        Map<String, Object> dataMap = Maps.newHashMap();
        IObjectData data = ObjectDataDocument.of(dataMap).toObjectData();
        data.setTenantId(user.getTenantId());
        data.setDescribeApiName(API_NAME);
        data.set(DEFAULT_ROLES, roles);
        data.set(NOTICE_TYPES, Lists.newArrayList(SMS.getVia()));
        return channelConfigAccess.saveObjectData(user, data);
    }

    private void fillNoticeConfigByType(String mode, PartnerChannelManage.NoticeConfigArg noticeConfig, IObjectData channelConfigData) {
        NotifyViaEnums noticeEnum = NotifyViaEnums.of(mode);
        if (noticeEnum == null) {
            // mode
            throw new ValidateException(I18N.text(SFA_PARTNER_MANAGEMENT_CAN_NOT_SUPPORT_NOTIFICATION_TYPE));
        }
        switch (noticeEnum) {
            case SMS:
                fillSmsNoticeInfo(noticeConfig, channelConfigData);
                break;
            case OFFICIAL_ACCOUNT:
                fillOfficialAccountNoticeInfo();
                break;
            default:
                break;
        }
    }

    private void fillOfficialAccountNoticeInfo() {
        log.warn("fillOfficialAccountNoticeInfo");
    }

    private void fillSmsNoticeInfo(PartnerChannelManage.NoticeConfigArg noticeConfig, IObjectData channelConfigData) {
        List<String> smsPhones = noticeConfig.getSmsPhones();
        PartnerChannelManage.SmsContent smsPassContent = noticeConfig.getSmsPassContent();
        fillSmsContent(smsPassContent, channelConfigData, SMS_TEMPLATE_ID, SMS_CONTENT);
        PartnerChannelManage.SmsContent smsFailContent = noticeConfig.getSmsFailContent();
        fillSmsContent(smsFailContent, channelConfigData, SMS_TEMPLATE_FAIL_ID, SMS_FAIL_CONTENT);
        channelConfigData.set(PartnerChannelConfigModel.SMS_PHONES, smsPhones);
    }

    private void fillSmsContent(PartnerChannelManage.SmsContent smsContent, IObjectData channelConfigData, String templateField, String contentField) {
        if (smsContent == null) {
            channelConfigData.set(templateField, "");
            channelConfigData.set(contentField, "");
            return;
        }
        String templateId = smsContent.getTemplateId();
        channelConfigData.set(templateField, smsContent.getTemplateId());
        if (StringUtils.isBlank(templateId)) {
            channelConfigData.set(contentField, smsContent.getContent());
        } else {
            channelConfigData.set(contentField, "");
        }
    }

    public IObjectDescribe getPhoneFieldDescribe(User user) {
        PartnerChannelManage.AdmissionConfig admissionConfig = fetchChannelAdmissionConfig(user);
        String relatedObjectApiName = admissionConfig.getRelatedObjectApiName();
        if (StringUtils.isBlank(relatedObjectApiName)) {
            return null;
        }
        IObjectDescribe objectDescribe = describeEnhancer.fetchObject(user, relatedObjectApiName);
        if (objectDescribe == null) {
            log.warn("getPhoneFieldDescribe objectDescribe is null, relatedObjectApiName: {}", relatedObjectApiName);
            return null;
        }
        List<IFieldDescribe> filterFieldDescribe = objectDescribe
                .getFieldDescribes()
                .stream()
                .filter(field -> PHONE_FIELD_TYPE.equals(field.getType()))
                .collect(Collectors.toList());
        objectDescribe.setFieldDescribes(filterFieldDescribe);
        return objectDescribe;
    }

    private IObjectDescribe getPartnerLoginDescribe(User user) {
        try {
            RequestContextManager.getContext().setAttribute(RequestContext.DIRECT_KEY, true);
            return describeLogicService.findObject(user.getTenantId(), SFAPreDefineObject.Partner.getApiName());
        } catch (Exception e) {
            log.warn("PartnerChannelService#getPhoneFieldDescribe failed, tenant:{}", user.getTenantId(), e);
            return null;
        }
    }

    public List<PartnerChannelManage.DefaultRoleResult> getAppRoleList(User user) {
        PartnerChannelManage.AdmissionConfig admissionConfig = fetchChannelAdmissionConfig(user);
        String applyToApp = admissionConfig.getApplyToApp();
        if (StringUtils.isEmpty(applyToApp)) {
            return Lists.newArrayList();
        }
        ListAppOuterRolesByAppIdArg roleArg = new ListAppOuterRolesByAppIdArg();
        roleArg.setTenantId(user.getTenantIdInt());
        roleArg.setLinkAppId(AppIdMapping.getAppIdByName(applyToApp));
        RestResult<List<RoleInfoData>> listRestResult =
                appOuterRoleService.listAppOuterRolesByAppId(HeaderObj.newInstance(Integer.valueOf(user.getTenantId())), roleArg);
        if (listRestResult == null || CollectionUtils.isEmpty(listRestResult.getData())) {
            return Lists.newArrayList();
        }
        return convertDefaultRoles(listRestResult);
    }

    private List<PartnerChannelManage.DefaultRoleResult> convertDefaultRoles(RestResult<List<RoleInfoData>> listRestResult) {
        List<PartnerChannelManage.DefaultRoleResult> roles = Lists.newArrayList();
        for (RoleInfoData infoData : listRestResult.getData()) {
            PartnerChannelManage.DefaultRoleResult item = PartnerChannelManage.DefaultRoleResult.builder()
                    .roleName(infoData.getRoleName())
                    .roleCode(infoData.getRoleCode())
                    .build();
            roles.add(item);
        }
        return roles;
    }

    public Boolean initPartnerChannelMapping(User user) {
        try {
            partnerOpenService.initPartnerChannelMapping();
        } catch (Exception e) {
            log.warn("initPartnerChannelMapping error, tenant:{}", user.getTenantId(), e);
            return false;
        }
        return true;
    }

    public PartnerChannelManage.Result saveNoticeTypes(User user, String configId, List<String> noticeTypes) {
        IObjectData channelConfigData = channelConfigAccess.findChannelConfig(user, configId);
        channelConfigData.set(NOTICE_TYPES, noticeTypes);
        IObjectData objectData = channelConfigAccess.updateObjectDataByFields(user, channelConfigData, Lists.newArrayList(NOTICE_TYPES));
        PartnerChannelManage.ContentParamMapping contentParamMapping = getParamMapping(user);
        return buildResult(objectData, contentParamMapping);
    }

    public Boolean canProvisionSms(User user) {
        CheckSmsStatus.Arg arg = new CheckSmsStatus.Arg();
        arg.setTenantId(user.getTenantIdInt());
        try {
            CheckSmsStatus.Result smsStatus = smsServiceProxy.checkSmsStatus(arg);
            return smsStatus != null && smsStatus.getStatus() == 0;
        } catch (Exception e) {
            log.error("PartnerChannelService#canProvisionSms error, tenant:{}", user.getTenantId(), e);
        }
        return false;
    }

    public BaseObjectSaveAction.Result createContactByPartner(User user, String partnerId) {
        IObjectData partnerData = metaDataFindServiceExt.findObjectByIdIgnoreAll(user, partnerId, SFAPreDefineObject.Partner.getApiName());
        if (partnerData == null) {
            log.warn("createLinkRelationConsumer but partnerData is empty, tenant:{}, partnerId:{}", user.getTenantId(), partnerId);
            return null;
        }
        ActionContext actionContext = new ActionContext(RequestContextManager.getContext(), SFAPreDefine.Contact.getApiName(), SystemConstants.ActionCode.Add.getActionCode());
        BaseObjectSaveAction.Arg arg = buildContactAddArg(user, partnerData);
        return serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
    }

    private BaseObjectSaveAction.Arg buildContactAddArg(User user, IObjectData partnerData) {
        Map<String, Object> dataMap = buildContactDataMap(user, partnerData);
        ObjectDataDocument dataDocument = ObjectDataDocument.of(dataMap);
        return buildAddActionArg(dataDocument);
    }

    private Map<String, Object> buildContactDataMap(User user, IObjectData partnerData) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put(ObjectDataExt.TENANT_ID, user.getTenantId());
        dataMap.put(ObjectDataExt.RECORD_TYPE, CONTACT_PARTNER_TYPE);
        dataMap.put(ObjectDataExt.DESCRIBE_API_NAME, SFAPreDefineObject.Contact.getApiName());
        List<IObjectMappingRuleDetailInfo> mappingFields = getMappingFields(user);
        Optional.ofNullable(mappingFields).orElse(Lists.newArrayList())
                .forEach(mapping -> fillMappingFields(mapping, partnerData, dataMap));
        return dataMap;
    }

    private void fillMappingFields(IObjectMappingRuleDetailInfo mapping, IObjectData partnerData, Map<String, Object> dataMap) {
        String sourceField = mapping.getSourceFieldName();
        String targetField = mapping.getTargetFieldName();
        Object sourceValue = partnerData.get(sourceField);
        Object targetValue = null;
        List<IObjectMappingRuleEnumInfo> optionMappingInfo = mapping.getOptionMapping();
        if (CollectionUtils.isEmpty(optionMappingInfo)) {
            targetValue = sourceValue;
        } else {
            Map<String, String> optionMapping = optionMappingInfo.stream().collect(Collectors.toMap(IObjectMappingRuleEnumInfo::getSourceEnumCode, IObjectMappingRuleEnumInfo::getTargetEnumCode, (k1, k2) -> k1));
            if (sourceValue != null) {
                targetValue = optionMapping.get(sourceValue.toString());
            }
        }
        dataMap.put(targetField, targetValue);
    }

    public List<IObjectMappingRuleDetailInfo> getMappingFields(User user) {
        List<IObjectMappingRuleInfo> rules = objectMappingService.findByApiName(user, PARTNER_2_PARTNER_CONTACT);
        IObjectMappingRuleInfo mappingRule = rules.get(0);
        return mappingRule.getFieldMapping();
    }

    private BaseObjectSaveAction.Arg buildAddActionArg(ObjectDataDocument dataDocument) {
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(dataDocument);
        return arg;
    }

    public List<PartnerChannelManage.EnterpriseActivationSetting> saveActivationSettings(User user, List<PartnerChannelManage.EnterpriseActivationSetting> enterpriseActivationSettings) {
        // 先删后插
        PartnerChannelManage.ActivationSetting activationSetting = activationSettingAccess.deleteAllActivationSettings(user);
        paasRuleEngineService.deleteEngineRuleByRuleCodes(user, RuleEngineSceneEnums.CHANNEL_APPROVAL, SFAPreDefineObject.Partner.getApiName(), activationSetting.getDeleteIds());
        List<IObjectData> savedDataList = activationSettingAccess.saveActivationSettings(user, enterpriseActivationSettings);
        saveEngineRule(user, RuleEngineSceneEnums.CHANNEL_APPROVAL, SFAPreDefineObject.Partner.getApiName(), savedDataList);
        return enterpriseActivationSettings;
    }

    public void delActivationSettings(User user) {
        // 先删后
        PartnerChannelManage.ActivationSetting activationSetting = activationSettingAccess.deleteAllActivationSettings(user);
        paasRuleEngineService.deleteEngineRuleByRuleCodes(user, RuleEngineSceneEnums.CHANNEL_APPROVAL, SFAPreDefineObject.Partner.getApiName(), activationSetting.getDeleteIds());
    }

    public PartnerChannelManage.RegistrationApprovalResult queryActivationSettings(User user) {
        List<IObjectData> data = activationSettingAccess.queryAllActivationSettings(user);
        List<IObjectData> storedData = data.stream()
                .sorted(Comparator.comparingInt(activationSetting -> activationSetting.get(SignSchemeModel.PRIORITY, Integer.class, 0)))
                .collect(Collectors.toList());
        List<PartnerChannelManage.EnterpriseActivationSetting> enterpriseActivationSettings = Lists.newArrayList();
        storedData.forEach(d -> enterpriseActivationSettings.add(buildEnterpriseActivationSetting(d)));
        PartnerChannelManage.RegistrationApprovalResult result = PartnerChannelManage.RegistrationApprovalResult.builder()
                .enterpriseActivationSettings(enterpriseActivationSettings)
                .customText(channelConfigAccess.fetchCustomText(user, REGISTER_CUSTOM_TEXT))
                .conditionType(ConditionType.ALL.getType())
                .build();
        if (CollectionUtils.isNotEmpty(enterpriseActivationSettings)) {
            if (enterpriseActivationSettings.size() > 1) {
                result.setConditionType(ConditionType.CONDITION.getType());
            } else {
                String conditionType = getConditionType(enterpriseActivationSettings.get(0).getCondition());
                result.setConditionType(conditionType);
            }
        }
        return result;
    }

    private static PartnerChannelManage.EnterpriseActivationSetting buildEnterpriseActivationSetting(IObjectData d) {
        if (d == null) {
            return PartnerChannelManage.EnterpriseActivationSetting.builder().effective(false).build();
        }
        String expireCycleData = d.get(EnterpriseActivationSettingModel.EXPIRE_CYCLE_DATA, String.class);
        Integer cycleDay = null;
        Integer cycleMonth = null;
        if (StringUtils.isNotBlank(expireCycleData)) {
            String[] split = expireCycleData.split("-");
            if (split.length == 2) {
                cycleMonth = Integer.valueOf(split[0]);
                cycleDay = Integer.valueOf(split[1]);
            }
        }
        String condition = d.get(EnterpriseActivationSettingModel.CONDITION, String.class);
        PartnerChannelManage.EnterpriseActivationSetting item = PartnerChannelManage.EnterpriseActivationSetting.builder()
                .enterpriseActivationSettingId(d.getId())
                .condition(condition)
                .defaultRoles(d.get(EnterpriseActivationSettingModel.DEFAULT_ROLES, List.class, Lists.newArrayList()))
                .enterpriseType(d.get(EnterpriseActivationSettingModel.ENTERPRISE_TYPE, String.class, NON_CRM.getType()))
                .recyclingMode(d.get(EnterpriseActivationSettingModel.RECYCLING_MODE, String.class, FIXED_DAY.getMode()))
                .expireDays(d.get(EnterpriseActivationSettingModel.EXPIRE_DAYS, Integer.class))
                .expireCycleDay(cycleDay)
                .expireCycleMonth(cycleMonth)
                .priority(d.get(EnterpriseActivationSettingModel.PRIORITY, Integer.class, 1))
                .build();
        return item;
    }

    public String getConditionType(String condition) {
        if (StringUtils.isBlank(condition)) {
            return ConditionType.ALL.getType();
        }
        UseRangeFieldDataRender.UseRangeInfo useRangeInfo = parseConditionObject(condition);
        return useRangeInfo.getType();
    }

    public UseRangeFieldDataRender.UseRangeInfo parseConditionObject(@NotNull String condition) {
        try {
            return JSON.parseObject(condition, UseRangeFieldDataRender.UseRangeInfo.class);
        } catch (Exception e) {
            log.error("parseConditionObject error, condition:{}", condition, e);
            throw new ValidateException(I18N.text(SAF_PRM_APP_NOT_EXISTS, "condition"));
        }
    }

    public PartnerChannelManage.ApprovalNotice toggleSwitchStatus(User user, @NotNull PartnerChannelManage.SwitchItem switchItem) {
        return toggleStatus(user, switchItem);
    }

    private PartnerChannelManage.ApprovalNotice toggleStatus(User user, @NotNull PartnerChannelManage.SwitchItem switchItem) {
        NotifyViaEnums notifyVia = NotifyViaEnums.of(switchItem.getNotifyVia());
        BizScopeEnums bizScope = BizScopeEnums.fromString(switchItem.getBizScope());
        // 根据 approvalNoticeID 找到数据，更新 enabled 字段。
        IObjectData data = approvalNoticeAccess.queryApprovalNoticeById(user, switchItem.getApprovalNoticeId());
        if (data == null) {
            IObjectData defaultApprovalNoticeData = createDefaultApprovalNoticeData(user, bizScope, notifyVia, switchItem.getEnabled());
            return buildApprovalNotice(defaultApprovalNoticeData);
        }
        Boolean originalValue = data.get(ApprovalNoticeModel.ENABLED, Boolean.class);
        if (originalValue == null || originalValue.equals(switchItem.getEnabled())) {
            return buildApprovalNotice(data);
        }
        data.set(ApprovalNoticeModel.ENABLED, switchItem.getEnabled());
        metaDataFindServiceExt.bulkUpdateByFields(user, Lists.newArrayList(data), Lists.newArrayList(ApprovalNoticeModel.ENABLED));
        return buildApprovalNotice(data);
    }

    private PartnerChannelManage.ApprovalNotice buildApprovalNotice(IObjectData data) {
        return PartnerChannelManage.ApprovalNotice.builder()
                .enabled(data.get(ApprovalNoticeModel.ENABLED, Boolean.class))
                .approvalNoticeId(data.getId())
                .notifyVia(data.get(ApprovalNoticeModel.NOTIFY_VIA, String.class))
                .sender(data.get(ApprovalNoticeModel.SENDER, String.class))
                .receiver(data.get(ApprovalNoticeModel.RECEIVER, List.class, Lists.newArrayList()))
                .aplApiName(data.get(ApprovalNoticeModel.APL_API_NAME, String.class))
                .actionVia(data.get(ApprovalNoticeModel.ACTION_VIA, String.class))
                .bizScope(data.get(ApprovalNoticeModel.BIZ_SCOPE, String.class))
                .noticeIds(data.get(ApprovalNoticeModel.NOTICE_IDS, List.class, Lists.newArrayList()))
                .build();
    }

    private IObjectData createDefaultApprovalNoticeData(User user, BizScopeEnums bizScope, @NotNull NotifyViaEnums via, boolean enabled) {
        PartnerChannelManage.ApprovalNotice approvalNotice = PartnerChannelManage.ApprovalNotice.builder()
                .enabled(enabled)
                .notifyVia(via.getVia())
                .bizScope(bizScope.getScope())
                .build();
        return approvalNoticeAccess.saveApprovalNotice(user, approvalNotice);
    }

    public PartnerChannelManage.NoticeInstanceResult saveEmailInstance(User user, PartnerChannelManage.ApprovalNoticeEmailInstanceArg emailArg) {
        IObjectData approvalNoticeData = queryOrCreateApprovalNoticeData(user, emailArg.getApprovalNoticeId(), emailArg.getBizScope(), EMAIL.getVia(), false);
        List<String> noticeIds = approvalNoticeData.get(ApprovalNoticeModel.NOTICE_IDS, List.class, Lists.newArrayList());
        prmEmailAccess.deletedEmailByIds(user, Lists.newArrayList(noticeIds));
        // 分别保存通过和不通过的短信
        PartnerChannelManage.PrmEmail passEmail = emailArg.getPassEmail();
        PartnerChannelManage.PrmEmail nonPassEmail = emailArg.getNonPassEmail();
        String passEmailId = prmEmailAccess.saveEmail(user, passEmail);
        String nonPassEmailId = prmEmailAccess.saveEmail(user, nonPassEmail);
        approvalNoticeData.set(ApprovalNoticeModel.NOTICE_IDS, Lists.newArrayList(passEmailId, nonPassEmailId));
        approvalNoticeData.set(ApprovalNoticeModel.RECEIVER, emailArg.getReceiver());
        approvalNoticeData.set(ApprovalNoticeModel.SENDER, emailArg.getSender());
        approvalNoticeData.set(ApprovalNoticeModel.APL_API_NAME, emailArg.getAplApiName());
        metaDataFindServiceExt.bulkUpdateByFields(user, Lists.newArrayList(approvalNoticeData),
                Lists.newArrayList(ApprovalNoticeModel.NOTICE_IDS, ApprovalNoticeModel.RECEIVER, ApprovalNoticeModel.SENDER, ApprovalNoticeModel.APL_API_NAME));
        return PartnerChannelManage.NoticeInstanceResult.builder()
                .approvalNoticeId(approvalNoticeData.getId())
                .bizScope(approvalNoticeData.get(ApprovalNoticeModel.BIZ_SCOPE, String.class))
                .passEmail(passEmail)
                .nonPassEmail(nonPassEmail)
                .notifyVia(approvalNoticeData.get(ApprovalNoticeModel.NOTIFY_VIA, String.class))
                .receiver(emailArg.getReceiver())
                .sender(emailArg.getSender())
                .build();
    }

    public PartnerChannelManage.NoticeInstanceResult saveSmsInstance(User user, PartnerChannelManage.ApprovalNoticeSmsInstanceArg smsArg) {
        IObjectData approvalNoticeData = queryOrCreateApprovalNoticeData(user, smsArg.getApprovalNoticeId(), smsArg.getBizScope(), SMS.getVia(), false);
        List<String> noticeIds = approvalNoticeData.get(ApprovalNoticeModel.NOTICE_IDS, List.class, Lists.newArrayList());
        prmSmsAccess.deletedSmsConfigByIds(user, Lists.newArrayList(noticeIds));
        // 分别保存通过和不通过的短信
        PrmManagementModel.ShortMessage passSms = smsArg.getPassSms();
        PrmManagementModel.ShortMessage nonPassSms = smsArg.getNonPassSms();
        String passSmsId = prmSmsAccess.saveSms(user, passSms);
        String nonPassSmsId = prmSmsAccess.saveSms(user, nonPassSms);
        approvalNoticeData.set(ApprovalNoticeModel.NOTICE_IDS, Lists.newArrayList(passSmsId, nonPassSmsId));
        approvalNoticeData.set(ApprovalNoticeModel.RECEIVER, smsArg.getReceiver());
        approvalNoticeData.set(ApprovalNoticeModel.APL_API_NAME, smsArg.getAplApiName());
        metaDataFindServiceExt.bulkUpdateByFields(user, Lists.newArrayList(approvalNoticeData),
                Lists.newArrayList(ApprovalNoticeModel.NOTICE_IDS, ApprovalNoticeModel.RECEIVER, ApprovalNoticeModel.APL_API_NAME));
        return PartnerChannelManage.NoticeInstanceResult.builder()
                .approvalNoticeId(approvalNoticeData.getId())
                .bizScope(approvalNoticeData.get(ApprovalNoticeModel.BIZ_SCOPE, String.class))
                .notifyVia(approvalNoticeData.get(ApprovalNoticeModel.NOTIFY_VIA, String.class))
                .receiver(smsArg.getReceiver())
                .passSms(passSms)
                .nonPassSms(nonPassSms)
                .build();
    }

    public PartnerChannelManage.NoticeInstanceResult queryNoticeInstance(User user, PartnerChannelManage.NoticeInstanceArg instanceArg) {
        IObjectData data = approvalNoticeAccess.queryApprovalNoticeById(user, instanceArg.getApprovalNoticeId());
        if (data == null) {
            return PartnerChannelManage.NoticeInstanceResult.builder().build();
        }
        List<String> noticeInstanceIds = data.get(ApprovalNoticeModel.NOTICE_IDS, List.class, Lists.newArrayList());
        PartnerChannelManage.NoticeInstanceResult result = PartnerChannelManage.NoticeInstanceResult.builder()
                .approvalNoticeId(instanceArg.getApprovalNoticeId())
                .bizScope(data.get(ApprovalNoticeModel.BIZ_SCOPE, String.class))
                .notifyVia(data.get(ApprovalNoticeModel.NOTIFY_VIA, String.class))
                .receiver(data.get(ApprovalNoticeModel.RECEIVER, List.class, Lists.newArrayList()))
                .sender(data.get(ApprovalNoticeModel.SENDER, String.class))
                .build();
        NotifyViaEnums notifyVia = NotifyViaEnums.of(instanceArg.getNotifyVia());
        switch (notifyVia) {
            case SMS:
                buildSmsNoticeInstance(user, result, noticeInstanceIds);
                break;
            case EMAIL:
                buildEmailNoticeInstance(user, result, noticeInstanceIds);
                break;
            default:
                break;
        }
        return result;
    }

    private void buildEmailNoticeInstance(User user, PartnerChannelManage.NoticeInstanceResult result, List<String> noticeInstanceIds) {
        List<IObjectData> data = prmEmailAccess.queryEmailByIds(user, noticeInstanceIds);
        data.forEach(d -> {
            PartnerChannelManage.PrmEmail item = buildEmailMessage(d);
            NoticeInstanceCategoryEnums instanceCategory = NoticeInstanceCategoryEnums.fromString(item.getCategory());
            if (NON_PASS == instanceCategory) {
                result.setNonPassEmail(item);
            } else if (PASS == instanceCategory) {
                result.setPassEmail(item);
            }
        });
    }

    public PartnerChannelManage.PrmEmail buildEmailMessage(IObjectData d) {
        String category = d.get(PrmEmailModel.CATEGORY, String.class);
        return PartnerChannelManage.PrmEmail.builder()
                .emailId(d.getId())
                .emailType(d.get(PrmEmailModel.EMAIL_TYPE, String.class))
                .objectApiName(d.get(PrmEmailModel.RELATED_OBJECT_API_NAME, String.class))
                .templateId(d.get(PrmEmailModel.TEMPLATE_ID, String.class))
                .category(category)
                .build();
    }

    public void buildSmsNoticeInstance(User user, PartnerChannelManage.NoticeInstanceResult result, List<String> noticeInstanceIds) {
        List<IObjectData> data = prmSmsAccess.querySmsByIds(user, noticeInstanceIds);
        data.forEach(d -> {
            PrmManagementModel.ShortMessage item = buildShortMessage(d);
            NoticeInstanceCategoryEnums instanceCategory = NoticeInstanceCategoryEnums.fromString(item.getCategory());
            if (NON_PASS == instanceCategory) {
                result.setNonPassSms(item);
            } else if (PASS == instanceCategory) {
                result.setPassSms(item);
            }
        });
    }

    public PrmManagementModel.ShortMessage buildShortMessage(IObjectData d) {
        String category = d.get(PrmSmsModel.CATEGORY, String.class);
        String smsContentParamJson = d.get(PrmSmsModel.SMS_CONTENT_PARAM, String.class);
        List<PrmManagementModel.ContentParam> smsContentParamList = JSON.parseObject(smsContentParamJson, List.class);
        return PrmManagementModel.ShortMessage.builder()
                .smsId(d.getId())
                .smsType(d.get(PrmSmsModel.SMS_TYPE, String.class))
                .content(d.get(PrmSmsModel.SMS_CONTENT, String.class))
                .templateId(d.get(PrmSmsModel.TEMPLATE_ID, String.class))
                .category(category)
                .smsContentParam(smsContentParamList)
                .build();
    }

    public List<PartnerChannelManage.ApprovalNotice> queryApprovalNotice(User user) {
        return queryNoticeByBizScope(user, BizScopeEnums.REGISTER);
    }

    public List<PartnerChannelManage.ApprovalNotice> queryNoticeByBizScope(User user, BizScopeEnums bizScope) {
        List<IObjectData> data = approvalNoticeAccess.queryAllApprovalNoticeByScope(user, bizScope);
        List<PartnerChannelManage.ApprovalNotice> approvalNotices = Lists.newArrayList();
        data.forEach(d -> {
            PartnerChannelManage.ApprovalNotice item = PartnerChannelManage.ApprovalNotice.builder()
                    .approvalNoticeId(d.getId())
                    .notifyVia(d.get(ApprovalNoticeModel.NOTIFY_VIA, String.class))
                    .aplApiName(d.get(ApprovalNoticeModel.APL_API_NAME, String.class))
                    .receiver(d.get(ApprovalNoticeModel.RECEIVER, List.class, Lists.newArrayList()))
                    .sender(d.get(ApprovalNoticeModel.SENDER, String.class))
                    .enabled(d.get(ApprovalNoticeModel.ENABLED, Boolean.class, Boolean.FALSE))
                    .bizScope(d.get(ApprovalNoticeModel.BIZ_SCOPE, String.class))
                    .noticeIds(d.get(ApprovalNoticeModel.NOTICE_IDS, List.class))
                    .build();
            approvalNotices.add(item);
        });
        return approvalNotices;
    }

    public PartnerChannelManage.NoticeInstanceResult saveNoticeApl(User user, PartnerChannelManage.NoticeAplArg aplArg) {
        IObjectData approvalNoticeData = queryOrCreateApprovalNoticeData(user, aplArg.getApprovalNoticeId(), aplArg.getBizScope(), aplArg.getNotifyVia(), false);
        approvalNoticeData.set(ApprovalNoticeModel.APL_API_NAME, aplArg.getAplApiName());
        metaDataFindServiceExt.bulkUpdateByFields(user, Lists.newArrayList(approvalNoticeData), Lists.newArrayList(ApprovalNoticeModel.APL_API_NAME));
        return PartnerChannelManage.NoticeInstanceResult.builder()
                .bizScope(aplArg.getBizScope())
                .aplApiName(aplArg.getAplApiName())
                .approvalNoticeId(approvalNoticeData.getId())
                .notifyVia(aplArg.getNotifyVia())
                .build();
    }

    private IObjectData queryOrCreateApprovalNoticeData(User user, String approvalNoticeId, String bizScopeStr, String notifyViaStr, boolean enable) {
        BizScopeEnums bizScope = BizScopeEnums.fromString(bizScopeStr);
        NotifyViaEnums notifyVia = NotifyViaEnums.of(notifyViaStr);
        IObjectData approvalNoticeData = approvalNoticeAccess.queryApprovalNoticeById(user, approvalNoticeId);
        if (approvalNoticeData == null) {
            approvalNoticeData = createDefaultApprovalNoticeData(user, bizScope, notifyVia, enable);
        }
        return approvalNoticeData;
    }

    public List<PartnerChannelManage.ProvisionScheme> queryProvisionScheme(User user) {
        List<IObjectData> data = provisionSchemeAccess.queryAllProvisionScheme(user);
        return buildProvisionSchemeList(user, data);
    }

    private List<PartnerChannelManage.ProvisionScheme> buildProvisionSchemeList(User user, List<IObjectData> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }
        List<PartnerChannelManage.ProvisionScheme> provisionScheme = Lists.newArrayList();
        data.forEach(d -> provisionScheme.add(buildProvisionScheme(user, d)));
        return provisionScheme.stream()
                .sorted(Comparator.comparingInt(PartnerChannelManage.ProvisionScheme::getPriority))
                .collect(Collectors.toList());
    }

    private PartnerChannelManage.ProvisionScheme buildProvisionScheme(User user, IObjectData d) {
        String condition = d.get(ProvisionSchemeModel.CONDITION, String.class);
        String conditionType = getConditionType(condition);
        String aplName = d.get(APL_API_NAME, String.class);
        if (StringUtils.isNotBlank(aplName)) {
            conditionType = ConditionType.APL.getType();
        }
        List<String> provisionIds = d.get(PROVISION_IDS, List.class, Lists.newArrayList());
        List<Map<String, String>> provisionsNameMappingList = getProvisionsNameMappingList(user, provisionIds);
        PartnerChannelManage.ProvisionScheme buildItem = PartnerChannelManage.ProvisionScheme.builder()
                .provisionSchemeId(d.getId())
                .schemeName(d.get(SCHEME_NAME, String.class))
                .mustRead(d.get(MUST_READ, Boolean.class, Boolean.TRUE))
                .provisionIds(provisionIds)
                .provisionsNameMappingList(provisionsNameMappingList)
                .condition(d.get(ProvisionSchemeModel.CONDITION, String.class))
                .conditionType(conditionType)
                .aplApiName(aplName)
                .priority(d.get(ProvisionSchemeModel.PRIORITY, Integer.class, 0))
                .build();
        return buildItem;
    }

    private List<Map<String, String>> getProvisionsNameMappingList(User user, List<String> provisionIds) {
        List<Map<String, String>> provisionsNameMappingList = Lists.newArrayList();
        Map<String, String> nameMapping = provisionSchemeAccess.queryProvisionSchemeNameByIds(user, provisionIds);
        nameMapping.forEach((key, value) -> {
            Map<String, String> map = Maps.newHashMap();
            map.put(key, value);
            provisionsNameMappingList.add(map);
        });
        return provisionsNameMappingList;
    }

    public List<PartnerChannelManage.ProvisionScheme> saveProvisionScheme(User user, PartnerChannelManage.ProvisionSchemeArg provisionSchemeArg) {
        Set<String> originalProvisionSchemeData = provisionSchemeAccess.deleteAllProvisionScheme(user);
        paasRuleEngineService.deleteEngineRuleByRuleCodes(user, RuleEngineSceneEnums.CHANNEL_PROVISION, SFAPreDefineObject.Partner.getApiName(), originalProvisionSchemeData);
        List<IObjectData> dataList = provisionSchemeAccess.saveProvisionScheme(user, provisionSchemeArg.getProvisionScheme());
        saveEngineRule(user, RuleEngineSceneEnums.CHANNEL_PROVISION, SFAPreDefineObject.Partner.getApiName(), dataList);
        return buildProvisionSchemeList(user, dataList);
    }

    public void delProvisionScheme(User user) {
        Set<String> originalProvisionSchemeData = provisionSchemeAccess.deleteAllProvisionScheme(user);
        paasRuleEngineService.deleteEngineRuleByRuleCodes(user, RuleEngineSceneEnums.CHANNEL_PROVISION, SFAPreDefineObject.Partner.getApiName(), originalProvisionSchemeData);
    }

    public IObjectDescribe getEmailFieldDescribe(User user) {
        PartnerChannelManage.AdmissionConfig admissionConfig = fetchChannelAdmissionConfig(user);
        String relatedObjectApiName = admissionConfig.getRelatedObjectApiName();
        IObjectDescribe objectDescribe = describeEnhancer.fetchObject(user, relatedObjectApiName);
        if (objectDescribe == null) {
            log.warn("getEmailFieldDescribe objectDescribe is null, relatedObjectApiName: {}", relatedObjectApiName);
            return null;
        }
        List<IFieldDescribe> filterFieldDescribe = objectDescribe
                .getFieldDescribes()
                .stream()
                .filter(field -> EMAIL.getVia().equals(field.getType()))
                .collect(Collectors.toList());
        objectDescribe.setFieldDescribes(filterFieldDescribe);
        return objectDescribe;
    }

    public List<PartnerChannelManage.SignScheme> querySignSchemeList(User user) {
        List<IObjectData> dataList = signSchemeAccess.queryAllSignScheme(user);
        return assembleSignSchemeList(user, dataList);
    }

    public List<PartnerChannelManage.SimpleSignScheme> querySimpleSignSchemeList(User user) {
        List<IObjectData> dataList = signSchemeAccess.queryAllSignScheme(user);
        List<PartnerChannelManage.SimpleSignScheme> simpleSignSchemeList = converter.convertInPutDataList(dataList, PartnerChannelManage.SimpleSignScheme.class);
        Set<String> agreementIds = simpleSignSchemeList.stream()
                .map(PartnerChannelManage.SimpleSignScheme::getAgreementId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        Map<String, String> partnerAgreementNameMapping = getPartnerAgreementNameMappingByIds(user, agreementIds);
        simpleSignSchemeList.forEach(simpleSignScheme -> simpleSignScheme.setAgreementName(partnerAgreementNameMapping.get(simpleSignScheme.getAgreementId())));
        return storedSimpleSignSchemeByPriority(simpleSignSchemeList);
    }

    private List<PartnerChannelManage.SimpleSignScheme> storedSimpleSignSchemeByPriority(List<PartnerChannelManage.SimpleSignScheme> simpleSignSchemeList) {
        return simpleSignSchemeList.stream()
                .sorted(Comparator.comparingInt(PartnerChannelManage.SimpleSignScheme::getPriority))
                .collect(Collectors.toList());
    }

    public List<PartnerChannelManage.SignScheme> queryPushedSignSchemeList(User user) {
        List<IObjectData> dataList = signSchemeAccess.queryPushedSignSchemeList(user);
        return assembleSignSchemeList(user, dataList);
    }

    public List<PartnerChannelManage.SignScheme> assembleSignSchemeList(User user, List<IObjectData> dataList) {
        List<PartnerChannelManage.SignScheme> signSchemeList = Lists.newArrayList();
        // 组装 signScheme
        dataList.forEach(data -> signSchemeList.add(assembleSignScheme(data)));
        // 获取所有提醒数据
        List<PartnerChannelManage.ApprovalNotice> allNoticeData = getNoticeData(user, signSchemeList);
        Map<String, PartnerChannelManage.ApprovalNotice> approvalNoticeMapping = allNoticeData.stream().collect(Collectors.toMap(d -> d.getApprovalNoticeId(), d -> d));
        // 协议名称映射
        Map<String, String> partnerAgreementNameMapping = getPartnerAgreementNameMapping(user, signSchemeList);
        // 过期提醒设置
        Map<String, PartnerChannelManage.ExpireReminderTypeView> expireRemindTypeViewMapping = getExpireRemindType(user, signSchemeList);
        // 提醒成员
        Map<String, PartnerChannelManage.ExpireReminderPersonView> expireReminderPersonViewMapping = getExpireReminderPersonView(user, signSchemeList);
        // 组装
        IObjectDescribe partnerAgreementDetailDescribe = getPartnerAgreementDetailDescribe(user);
        assembleSignSchemeExtend(partnerAgreementDetailDescribe, signSchemeList, approvalNoticeMapping, partnerAgreementNameMapping, expireRemindTypeViewMapping, expireReminderPersonViewMapping);

        return signSchemeList;
    }

    private IObjectDescribe getPartnerAgreementDetailDescribe(User user) {
        try {
            return describeLogicService.findObject(user.getTenantId(), SFAPreDefineObject.PartnerAgreementDetail.getApiName());
        } catch (Exception e) {
            log.warn("getPartnerAgreementDetailDescribe error", e);
        }
        return null;
    }

    private void assembleSignSchemeExtend(IObjectDescribe partnerAgreementDetailDescribe, List<PartnerChannelManage.SignScheme> signSchemeList,
                                          Map<String, PartnerChannelManage.ApprovalNotice> approvalNoticeMapping,
                                          Map<String, String> partnerAgreementNameMapping,
                                          Map<String, PartnerChannelManage.ExpireReminderTypeView> expireRemindTypeViewMapping,
                                          Map<String, PartnerChannelManage.ExpireReminderPersonView> expireReminderPersonViewMapping) {
        for (PartnerChannelManage.SignScheme signScheme : signSchemeList) {
            List<PartnerChannelManage.ApprovalNotice> approvalNoticeList = Lists.newArrayList();
            for (String noticeId : Optional.ofNullable(signScheme.getNoticeIds()).orElse(Lists.newArrayList())) {
                if (approvalNoticeMapping.get(noticeId) != null) {
                    approvalNoticeList.add(approvalNoticeMapping.get(noticeId));
                }
            }
            signScheme.setApprovalNoticeList(approvalNoticeList);
            signScheme.setAgreementName(partnerAgreementNameMapping.get(signScheme.getAgreementId()));
            signScheme.setExpireReminderTypeView(expireRemindTypeViewMapping.get(signScheme.getSignSchemeId()));
            signScheme.setExpireReminderPersonView(expireReminderPersonViewMapping.get(signScheme.getSignSchemeId()));
        }
    }

    private Map<String, PartnerChannelManage.ExpireReminderPersonView> getExpireReminderPersonView(User user, List<PartnerChannelManage.SignScheme> signSchemeList) {
        Map<String, PartnerChannelManage.ExpireReminderPersonView> signSchemeIdViewMapping = Maps.newHashMap();
        Set<String> signSchemeIds = signSchemeList.stream().map(PartnerChannelManage.SignScheme::getSignSchemeId).collect(Collectors.toSet());
        List<IObjectData> expireReminderPersonDataList = expireReminderPersonAccess.queryExpireReminderPersonBySignSchemeIds(user, signSchemeIds);
        Map<String, List<IObjectData>> groupedExpireReminderPerson = expireReminderPersonDataList.stream()
                .collect(Collectors.groupingBy(data -> ObjectDataUtils.getValue(data, ExpireReminderPersonModel.SIGN_SCHEME_ID, String.class, "")));
        for (String signSchemeId : signSchemeIds) {
            List<IObjectData> reminderPerson = groupedExpireReminderPerson.getOrDefault(signSchemeId, Lists.newArrayList());
            List<PartnerChannelManage.ReminderPerson> internalPerson = assembleInternalPerson(reminderPerson);
            List<PartnerChannelManage.ReminderPerson> externalPerson = assembleExternalPerson(reminderPerson);
            PartnerChannelManage.ExpireReminderPersonView expireReminderPersonView = PartnerChannelManage.ExpireReminderPersonView.builder()
                    .internalPerson(internalPerson)
                    .externalPerson(externalPerson)
                    .build();
            signSchemeIdViewMapping.put(signSchemeId, expireReminderPersonView);
        }
        return signSchemeIdViewMapping;
    }

    private List<PartnerChannelManage.ReminderPerson> assembleExternalPerson(List<IObjectData> reminderPerson) {
        List<IObjectData> externalPersonDataList = reminderPerson.stream().filter(data -> {
            String identityStr = ObjectDataUtils.getValue(data, ExpireReminderPersonModel.IDENTITY, String.class, "");
            return IdentityEnums.EXTERNAL == IdentityEnums.of(identityStr);
        }).collect(Collectors.toList());
        return assemblePerson(externalPersonDataList);
    }

    private List<PartnerChannelManage.ReminderPerson> assembleInternalPerson(List<IObjectData> reminderPerson) {
        List<IObjectData> internalPersonDataList = reminderPerson.stream().filter(data -> {
            String identityStr = ObjectDataUtils.getValue(data, ExpireReminderPersonModel.IDENTITY, String.class, "");
            return IdentityEnums.INTERNAL == IdentityEnums.of(identityStr);
        }).collect(Collectors.toList());
        return assemblePerson(internalPersonDataList);
    }

    private List<PartnerChannelManage.ReminderPerson> assemblePerson(List<IObjectData> reminderPerson) {
        List<PartnerChannelManage.ReminderPerson> reminderPersonList = Lists.newArrayList();
        for (IObjectData data : reminderPerson) {
            ChannelManagementDTO.ReminderPerson reminderPersonDTO = converter.convertDTO(data, ChannelManagementDTO.ReminderPerson.class);
            PartnerChannelManage.ReminderPerson person = PartnerChannelManage.ReminderPerson.builder()
                    .reminderPersonId(reminderPersonDTO.getReminderPersonId())
                    .signSchemeId(reminderPersonDTO.getSignSchemeId())
                    .identity(reminderPersonDTO.getIdentity())
                    .memberType(reminderPersonDTO.getMemberType())
                    .dataId(reminderPersonDTO.getDataId())
                    .build();
            reminderPersonList.add(person);
        }
        return reminderPersonList;
    }

    private PartnerChannelManage.SignScheme assembleSignScheme(IObjectData data) {
        ChannelManagementDTO.SignScheme signSchemeDTO = converter.convertDTO(data, ChannelManagementDTO.SignScheme.class);
        String conditionType = getConditionType(signSchemeDTO.getCondition());
        return PartnerChannelManage.SignScheme.builder()
                .signSchemeId(signSchemeDTO.getSignSchemeId())
                .schemeName(signSchemeDTO.getSchemeName())
                .agreementId(signSchemeDTO.getAgreementId())
                .condition(signSchemeDTO.getCondition())
                .conditionType(conditionType)
                .signMode(signSchemeDTO.getSignMode())
                .aplApiName(signSchemeDTO.getAplApiName())
                .activateRoles(signSchemeDTO.getActivateRoles())
                .noticeIds(signSchemeDTO.getNoticeIds())
                .priority(signSchemeDTO.getPriority())
                .reminderTrigger(signSchemeDTO.getReminderTrigger())
                .scheduleType(signSchemeDTO.getScheduleType())
                .pushed(signSchemeDTO.getPushed())
                .startDate(signSchemeDTO.getStartDate())
                .planDuration(signSchemeDTO.getPlanDuration())
                .planDurationUnit(signSchemeDTO.getPlanDurationUnit())
                .nextRenewalDate(signSchemeDTO.getNextRenewalDate())
                .renewalCycleTime(signSchemeDTO.getRenewalCycleTime())
                .renewalCycleUnit(signSchemeDTO.getRenewalCycleUnit())
                .renewalWindowStart(signSchemeDTO.getRenewalWindowStart())
                .renewalWindowEnd(signSchemeDTO.getRenewalWindowEnd())
                .renewalWindowUnit(signSchemeDTO.getRenewalWindowUnit())
                .renewalPage(signSchemeDTO.getRenewalPage())
                .overdueMessage(signSchemeDTO.getOverdueMessage())
                .varOverdueMessage(I18N.text(PRM_CHANNEL_VAR_MESSAGE_EXPIRED))
                .build();
    }

    private Map<String, PartnerChannelManage.ExpireReminderTypeView> getExpireRemindType(User user, List<PartnerChannelManage.SignScheme> signSchemeList) {
        Map<String, PartnerChannelManage.ExpireReminderTypeView> signSchemeIdViewMapping = Maps.newHashMap();
        Set<String> signSchemeIds = signSchemeList.stream().map(PartnerChannelManage.SignScheme::getSignSchemeId).collect(Collectors.toSet());
        List<IObjectData> expireReminderTypes = expireReminderTypeAccess.queryExpireReminderTypesBySignSchemeIds(user, signSchemeIds);
        Map<String, List<IObjectData>> groupedExpireReminderTypes = expireReminderTypes.stream()
                .collect(Collectors.groupingBy(data -> ObjectDataUtils.getValue(data, ExpireReminderTypeModel.SIGN_SCHEME_ID, String.class, "")));
        for (String signSchemeId : signSchemeIds) {
            List<IObjectData> reminderTypes = groupedExpireReminderTypes.getOrDefault(signSchemeId, Lists.newArrayList());
            PartnerChannelManage.ExpireReminderType autoExpireReminderType = assembleAutoExpireReminderType(reminderTypes);
            PartnerChannelManage.ExpireReminderType manualExpireReminderType = assembleManualExpireReminderType(reminderTypes);
            PartnerChannelManage.ExpireReminderTypeView reminderTypeView = PartnerChannelManage.ExpireReminderTypeView.builder()
                    .autoExpireReminderType(autoExpireReminderType)
                    .manualExpireReminderType(manualExpireReminderType)
                    .build();
            signSchemeIdViewMapping.put(signSchemeId, reminderTypeView);
        }
        return signSchemeIdViewMapping;
    }

    private PartnerChannelManage.ExpireReminderType assembleAutoExpireReminderType(List<IObjectData> reminderTypes) {
        List<IObjectData> autoDataList = reminderTypes.stream().filter(data -> ReminderTrigger.AUTO.getTrigger().equals(ObjectDataUtils.getValue(data, ExpireReminderTypeModel.REMINDER_TRIGGER, String.class, ""))).collect(Collectors.toList());
        return assembleExpireReminderType(autoDataList);
    }

    private PartnerChannelManage.ExpireReminderType assembleManualExpireReminderType(List<IObjectData> reminderTypes) {
        List<IObjectData> autoDataList = reminderTypes.stream().filter(data -> ReminderTrigger.MANUAL.getTrigger().equals(ObjectDataUtils.getValue(data, ExpireReminderTypeModel.REMINDER_TRIGGER, String.class, ""))).collect(Collectors.toList());
        return assembleExpireReminderType(autoDataList);
    }

    private PartnerChannelManage.ExpireReminderType assembleExpireReminderType(List<IObjectData> reminderTypeDataList) {
        PartnerChannelManage.ReminderType prmAlertWindowReminder = getReminderTypeByMethod(reminderTypeDataList, NotifyViaEnums.PRM_ALERT);
        PartnerChannelManage.ReminderType smsReminder = getReminderTypeByMethod(reminderTypeDataList, NotifyViaEnums.SMS);
        PartnerChannelManage.ReminderType emailReminder = getReminderTypeByMethod(reminderTypeDataList, NotifyViaEnums.EMAIL);
        PartnerChannelManage.ReminderType crmReminder = getReminderTypeByMethod(reminderTypeDataList, NotifyViaEnums.CRM);
        PartnerChannelManage.ReminderType prmCrmReminder = getReminderTypeByMethod(reminderTypeDataList, NotifyViaEnums.PRM_CRM);
        return PartnerChannelManage.ExpireReminderType.builder()
                .prmAlertWindowReminder(prmAlertWindowReminder)
                .smsReminder(smsReminder)
                .emailReminder(emailReminder)
                .crmReminder(crmReminder)
                .prmCrmReminder(prmCrmReminder)
                .build();
    }

    private PartnerChannelManage.ReminderType getReminderTypeByMethod(List<IObjectData> reminderTypeDataList, NotifyViaEnums notifyVia) {
        PartnerChannelManage.ReminderType reminderType = PartnerChannelManage.ReminderType.builder().build();
        reminderTypeDataList.stream().filter(data -> {
            String reminderMethod = ObjectDataUtils.getValue(data, ExpireReminderTypeModel.REMINDER_METHOD, String.class, "");
            return NotifyViaEnums.of(reminderMethod) == notifyVia;
        }).findAny().ifPresent(data -> {
            ChannelManagementDTO.ReminderType reminderTypeDTO = converter.convertDTO(data, ChannelManagementDTO.ReminderType.class);
            reminderType.setReminderTypeId(reminderTypeDTO.getReminderTypeId());
            reminderType.setReminderTrigger(reminderTypeDTO.getReminderTrigger());
            reminderType.setReminderMethod(reminderTypeDTO.getReminderMethod());
            reminderType.setSignSchemeId(reminderTypeDTO.getSignSchemeId());
            reminderType.setTimeUnit(reminderTypeDTO.getTimeUnit());
            reminderType.setReminderTime(reminderTypeDTO.getReminderTime());
            reminderType.setTemplateId(reminderTypeDTO.getTemplateId());
            reminderType.setMessage(reminderTypeDTO.getMessage());
            reminderType.setActivated(reminderTypeDTO.getActivated());
        });
        if (NotifyViaEnums.PRM_ALERT == notifyVia) {
            reminderType.setVarMessage(I18N.text(PRM_CHANNEL_VAR_MESSAGE));
        }
        return reminderType;
    }

    private Map<String, String> getPartnerAgreementNameMapping(User user, List<PartnerChannelManage.SignScheme> signSchemeList) {
        if (CollectionUtils.isEmpty(signSchemeList)) {
            return Maps.newHashMap();
        }
        Set<String> agreementIds = signSchemeList.stream()
                .map(PartnerChannelManage.SignScheme::getAgreementId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        return getPartnerAgreementNameMappingByIds(user, agreementIds);
    }

    private Map<String, String> getPartnerAgreementNameMappingByIds(User user, Set<String> agreementIds) {
        if (CollectionUtils.isEmpty(agreementIds)) {
            return Maps.newHashMap();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(query.getFilters(), ObjectDataExt.ID, agreementIds);
        query.setLimit(0);
        Map<String, String> mapping = metaDataFindServiceExt.findBySearchQueryWithFieldsIgnoreAll(user, SFAPreDefineObject.PartnerAgreement.getApiName(), query, Lists.newArrayList(ObjectDataExt.ID, ObjectDataExt.NAME))
                .stream().collect(Collectors.toMap(DBRecord::getId, IObjectData::getName, (k1, k2) -> k1));
        agreementIds.forEach(agreementId -> mapping.putIfAbsent(agreementId, null));
        return mapping;
    }

    private PartnerChannelManage.SignScheme assembleSignScheme(@NotNull IObjectData d, List<PartnerChannelManage.ApprovalNotice> allNoticeData, Map<String, String> partnerAgreementNameMapping) {
        String agreementId = d.get(SignSchemeModel.AGREEMENT_ID, String.class);
        List<String> noticeIds = d.get(SignSchemeModel.NOTICE_IDS, List.class, Lists.newArrayList());
        String condition = d.get(SignSchemeModel.CONDITION, String.class);
        String conditionType = getConditionType(condition);
        String aplName = d.get(SignSchemeModel.APL_API_NAME, String.class);
        return PartnerChannelManage.SignScheme.builder()
                .signSchemeId(d.getId())
                .schemeName(d.get(SignSchemeModel.SCHEME_NAME, String.class))
                .agreementId(agreementId)
                .agreementName(partnerAgreementNameMapping.get(agreementId))//根据id查name
                .condition(condition)
                .conditionType(conditionType)
                .signMode(d.get(SignSchemeModel.SIGN_MODE, String.class))
                .aplApiName(aplName)
                .activateRoles(d.get(SignSchemeModel.ACTIVATE_ROLES, List.class, Lists.newArrayList()))
                .noticeIds(noticeIds)
                .approvalNoticeList(allNoticeData)
                .priority(d.get(SignSchemeModel.PRIORITY, Integer.class, 0))
                .build();
    }

    private List<PartnerChannelManage.ApprovalNotice> getNoticeData(User user, List<PartnerChannelManage.SignScheme> signSchemeList) {
        List<String> allNoticeIds = getNoticeIds(signSchemeList);
        List<IObjectData> data = approvalNoticeAccess.queryApprovalNoticeByIds(user, allNoticeIds);
        return data.stream().map(this::buildApprovalNotice).collect(Collectors.toList());
    }

    private List<String> getNoticeIds(List<PartnerChannelManage.SignScheme> signSchemeList) {
        Set<String> noticeSet = Sets.newHashSet();
        signSchemeList.forEach(signScheme -> noticeSet.addAll(signScheme.getNoticeIds()));
        return Lists.newArrayList(noticeSet);
    }

    public PartnerChannelManage.SignScheme addOneTimeSignScheme(User user, PartnerChannelManage.OneTimeSignScheme oneTimeSignScheme) {
        // 保存审批提醒数据
        List<String> noticeIds = saveNoticeData(user, oneTimeSignScheme.getApprovalNotices());
        ChannelManagementDTO.SignScheme convertSignScheme = converter.convertDTO(oneTimeSignScheme, ChannelManagementDTO.SignScheme.class);
        convertSignScheme.setNoticeIds(noticeIds);
        IObjectData savedSignScheme = signSchemeAccess.saveSignScheme(user, convertSignScheme);
        return assembleSignScheme(savedSignScheme);
    }

    public PartnerChannelManage.SignScheme addFixedDataSignScheme(User user, PartnerChannelManage.FixedDateSignScheme fixedDateSignScheme) {
        List<String> noticeIds = saveNoticeData(user, fixedDateSignScheme.getApprovalNotices());
        ChannelManagementDTO.SignScheme convertSignScheme = converter.convertDTO(fixedDateSignScheme, ChannelManagementDTO.SignScheme.class);
        setDefaults4FixedData(convertSignScheme);
        convertSignScheme.setNoticeIds(noticeIds);
        IObjectData savedSignScheme = signSchemeAccess.saveSignScheme(user, convertSignScheme);
        // 保存到期提醒设置
        saveExpireReminderType(user, savedSignScheme.getId(), fixedDateSignScheme.getExpireReminderTypeView());
        // 保存提醒成员
        saveExpireReminderPerson(user, savedSignScheme.getId(), fixedDateSignScheme.getExpireReminderPersonView());
        return assembleSignScheme(savedSignScheme);
    }

    /**
     * 设置固定日签约方案的默认值
     *
     * @param signScheme 签约方案
     */
    private void setDefaults4FixedData(ChannelManagementDTO.SignScheme signScheme) {
        signScheme.setRenewalCycleTime(1);
        signScheme.setRenewalCycleUnit(TimeUnit.YEAR.getUnit());
        signScheme.setRenewalWindowUnit(TimeUnit.DAY.getUnit());
    }

    private void setDefaults4CycleData(ChannelManagementDTO.SignScheme signScheme) {
        signScheme.setRenewalWindowUnit(TimeUnit.DAY.getUnit());
    }

    public PartnerChannelManage.SignScheme addCycleDataSignScheme(User user, PartnerChannelManage.CycleSignScheme cycleSignScheme) {
        List<String> noticeIds = saveNoticeData(user, cycleSignScheme.getApprovalNotices());
        ChannelManagementDTO.SignScheme convertSignScheme = converter.convertDTO(cycleSignScheme, ChannelManagementDTO.SignScheme.class);
        setDefaults4CycleData(convertSignScheme);
        convertSignScheme.setNoticeIds(noticeIds);
        IObjectData savedSignScheme = signSchemeAccess.saveSignScheme(user, convertSignScheme);
        // 保存到期提醒设置
        saveExpireReminderType(user, savedSignScheme.getId(), cycleSignScheme.getExpireReminderTypeView());
        // 保存提醒成员
        saveExpireReminderPerson(user, savedSignScheme.getId(), cycleSignScheme.getExpireReminderPersonView());
        return assembleSignScheme(savedSignScheme);
    }

    public PartnerChannelManage.SignScheme signSchemeEdit(User user, PartnerChannelManage.SignScheme signScheme) {
        String signSchemeId = updateSignSchemeConfig(user, signScheme);
        if (StringUtils.isBlank(signSchemeId)) {
            log.warn("signSchemeEdit fail, signSchemeList is empty");
            return null;
        }
        IObjectData data = signSchemeAccess.querySignSchemeById(user, signSchemeId);
        List<PartnerChannelManage.SignScheme> signSchemeList = assembleSignSchemeList(user, Lists.newArrayList(data));
        if (CollectionUtils.isEmpty(signSchemeList)) {
            log.warn("signSchemeEdit fail, signSchemeList is empty");
            return null;
        }
        return signSchemeList.get(0);
    }

    private String updateSignSchemeConfig(User user, PartnerChannelManage.SignScheme signScheme) {
        // 更新协议方案
        updateSignScheme(user, signScheme);
        // 更新到期提醒设置
        updateExpireReminderType(user, signScheme.getSignSchemeId(), signScheme.getExpireReminderTypeView());
        // 更新提醒成员
        updateExpireReminderPerson(user, signScheme.getSignSchemeId(), signScheme.getExpireReminderPersonView());
        return signScheme.getSignSchemeId();
    }

    private void updateExpireReminderPerson(User user, String signSchemeId, PartnerChannelManage.ExpireReminderPersonView expireReminderPersonView) {
        List<PartnerChannelManage.ReminderPerson> expireReminderPersonList = Stream.concat(
                expireReminderPersonView.getExternalPerson().stream(),
                expireReminderPersonView.getInternalPerson().stream()
        ).collect(Collectors.toList());
        // 查处所有提醒人员
        List<IObjectData> originalExpireReminderPersonDataList = expireReminderPersonAccess.queryExpireReminderPersonBySignSchemeId(user, signSchemeId);
        if (notModifiedExpireReminderPerson(originalExpireReminderPersonDataList, expireReminderPersonList)) {
            return;
        }
        expireReminderPersonAccess.deleteItemsBySignSchemeId(user, signSchemeId);
        saveExpireReminderPerson(user, signSchemeId, expireReminderPersonView);
    }

    private boolean notModifiedExpireReminderPerson(List<IObjectData> originalExpireReminderPersonDataList, List<PartnerChannelManage.ReminderPerson> expireReminderPersonList) {
        if (originalExpireReminderPersonDataList.size() != expireReminderPersonList.size()) {
            return false;
        }
        return expireReminderPersonList.stream().noneMatch(p -> StringUtils.isBlank(p.getReminderPersonId()));
    }

    private void updateExpireReminderType(User user, String signSchemeId, PartnerChannelManage.ExpireReminderTypeView expireReminderTypeView) {
        List<IObjectData> expireReminderTypeDataList = expireReminderTypeAccess.queryExpireReminderTypesBySignSchemeId(user, signSchemeId);
        List<PartnerChannelManage.ReminderType> reminderTypeList = mergeExpireReminderType(expireReminderTypeView.getAutoExpireReminderType(), expireReminderTypeView.getManualExpireReminderType());
        modifyUpdateData(user, reminderTypeList, expireReminderTypeDataList);
    }

    private List<PartnerChannelManage.ReminderType> mergeExpireReminderType(PartnerChannelManage.ExpireReminderType autoExpireReminderType, PartnerChannelManage.ExpireReminderType manualExpireReminderType) {
        return Stream.concat(
                convertExpireReminderTypeList(autoExpireReminderType).stream(),
                convertExpireReminderTypeList(manualExpireReminderType).stream()
        ).collect(Collectors.toList());
    }

    private List<PartnerChannelManage.ReminderType> convertExpireReminderTypeList(PartnerChannelManage.ExpireReminderType expireReminderType) {
        PartnerChannelManage.ReminderType crmReminder = expireReminderType.getCrmReminder();
        PartnerChannelManage.ReminderType prmCrmReminder = expireReminderType.getPrmCrmReminder();
        PartnerChannelManage.ReminderType prmAlertWindowReminder = expireReminderType.getPrmAlertWindowReminder();
        PartnerChannelManage.ReminderType smsReminder = expireReminderType.getSmsReminder();
        PartnerChannelManage.ReminderType emailReminder = expireReminderType.getEmailReminder();
        List<PartnerChannelManage.ReminderType> reminderTypeList = Lists.newArrayList();
        reminderTypeList.add(crmReminder);
        reminderTypeList.add(prmCrmReminder);
        reminderTypeList.add(prmAlertWindowReminder);
        reminderTypeList.add(smsReminder);
        reminderTypeList.add(emailReminder);
        return reminderTypeList;
    }

    private void modifyUpdateData(User user, List<PartnerChannelManage.ReminderType> reminderTypeList, List<IObjectData> expireReminderTypeDataList) {
        Map<String, PartnerChannelManage.ReminderType> reminderTypeMapping = reminderTypeList.stream().
                filter(d -> StringUtils.isNotBlank(d.getReminderTypeId()))
                .collect(Collectors.toMap(PartnerChannelManage.ReminderType::getReminderTypeId, d -> d,
                        (k1, k2) -> k1));
        Map<String, IObjectData> objectDataMapping = expireReminderTypeDataList.stream()
                .collect(Collectors.toMap(DBRecord::getId, d -> d,
                        (k1, k2) -> k1));
        List<IObjectData> updateDataList = Lists.newArrayList();
        HashSet<String> changedFields = Sets.newHashSet();
        objectDataMapping.forEach((id, data) -> {
            PartnerChannelManage.ReminderType reminderType = reminderTypeMapping.get(id);
            if (reminderType == null) {
                return;
            }
            ChannelManagementDTO.ReminderType originalReminderTypeDTO = converter.convertDTO(data, ChannelManagementDTO.ReminderType.class);
            ChannelManagementDTO.ReminderType argReminderTypeDTO = converter.convertDTO(reminderType, ChannelManagementDTO.ReminderType.class);
            if (DiffUtils.hasChanged(originalReminderTypeDTO, argReminderTypeDTO)) {
                Set<String> fields = ObjectDataUtils.setFieldValue(data, argReminderTypeDTO);
                updateDataList.add(data);
                changedFields.addAll(fields);
            }
        });

        if (CollectionUtils.isNotEmpty(updateDataList)) {
            metaDataFindServiceExt.bulkUpdateByFields(user, updateDataList, Lists.newArrayList(changedFields));
        }
    }

    private void updateSignScheme(User user, PartnerChannelManage.SignScheme signScheme) {
        IObjectData originalSignSchemeData = signSchemeAccess.querySignSchemeById(user, signScheme.getSignSchemeId());
        ChannelManagementDTO.SignScheme originalSignScheme = converter.convertDTO(originalSignSchemeData, ChannelManagementDTO.SignScheme.class);
        ChannelManagementDTO.SignScheme argSignScheme = converter.convertDTO(signScheme, ChannelManagementDTO.SignScheme.class);
        ScheduleType scheduleType = ScheduleType.from(argSignScheme.getScheduleType());
        if (scheduleType == ScheduleType.FIXED_DATE) {
            setDefaults4FixedData(argSignScheme);
        } else if (scheduleType == ScheduleType.CYCLE) {
            setDefaults4CycleData(argSignScheme);
        } else if (scheduleType == ScheduleType.ONE_TIME) {
            setDefault4OneTime(argSignScheme);
        }

        if (!DiffUtils.hasChanged(originalSignScheme, argSignScheme)) {
            return;
        }
        Map<String, Object[]> changedFields = DiffUtils.getChangedFields(originalSignScheme, argSignScheme);
        Set<String> fieldApiNames = ObjectDataUtils.setFieldValue(originalSignSchemeData, argSignScheme, changedFields.keySet());
        signSchemeAccess.updateObjectDataByFields(user, originalSignSchemeData, fieldApiNames);
    }

    private static void setDefault4OneTime(ChannelManagementDTO.SignScheme argSignScheme) {
        argSignScheme.setRenewalCycleTime(null);
        argSignScheme.setRenewalCycleUnit(null);
        argSignScheme.setRenewalWindowUnit(null);
        argSignScheme.setRenewalWindowStart(null);
        argSignScheme.setRenewalWindowEnd(null);
    }

    private String saveSignSchemeConfig(User user, PartnerChannelManage.SignScheme signScheme) {
        // 保存审批提醒
        List<String> noticeIds = saveSignNotices(user, signScheme);
        signScheme.setNoticeIds(noticeIds);
        // 保存协议方案
        IObjectData signSchemeData = saveSignScheme(user, signScheme);
        // 保存到期提醒设置
        saveExpireReminderType(user, signSchemeData.getId(), signScheme.getExpireReminderTypeView());
        // 保存提醒成员
        saveExpireReminderPerson(user, signSchemeData.getId(), signScheme.getExpireReminderPersonView());
        return signSchemeData.getId();
    }

    public void deleteSignScheme(User user, String deletedSignSchemeId) {
        // 待删除的签约方案
        IObjectData originalSignSchemeData = signSchemeAccess.querySignSchemeById(user, deletedSignSchemeId);
        if (originalSignSchemeData == null) {
            return;
        }
//        channelCacheService.agreementDetailStatusChangeLock(user);
        ChannelManagementDTO.SignScheme signSchemeDTO = converter.convertDTO(originalSignSchemeData, ChannelManagementDTO.SignScheme.class);
        // 删除签约方案主体
        deleteSignScheme(user, originalSignSchemeData);
        // 删除规则
        paasRuleEngineService.deleteEngineRuleByRuleCodes(user, RuleEngineSceneEnums.CHANNEL_AGREEMENT, SFAPreDefineObject.Partner.getApiName(), Sets.newHashSet(deletedSignSchemeId));
        // 删除提醒配置
        deleteNoticeItems(user, signSchemeDTO.getNoticeIds());
        // 删除 expireReminderTypeView
        deleteExpireReminderTypeView(user, signSchemeDTO.getSignSchemeId());
        // 删除 expireReminderPersonView
        deleteExpireReminderPersonView(user, signSchemeDTO.getSignSchemeId());
        sendChangeAgreementDetailStatusMessage(user, signSchemeDTO.getSignSchemeId(), "delete");
    }

    private void deleteSignScheme(User user, IObjectData originalSignSchemeData) {
        serviceFacade.bulkDeleteDirect(Lists.newArrayList(originalSignSchemeData), user);
    }

    private void deleteNoticeItems(User user, List<String> noticeIds) {
        List<IObjectData> originalNoticeData = approvalNoticeAccess.queryApprovalNoticeByIds(user, noticeIds);
        Set<String> smsInstanceIds = Sets.newHashSet();
        Set<String> emailInstanceIds = Sets.newHashSet();
        for (IObjectData data : originalNoticeData) {
            String notifyViaStr = ObjectDataUtils.getValue(data, ApprovalNoticeModel.NOTIFY_VIA, String.class, null);
            if (StringUtils.isBlank(notifyViaStr)) {
                continue;
            }
            NotifyViaEnums notifyVia = NotifyViaEnums.of(notifyViaStr);
            if (NotifyViaEnums.SMS == notifyVia) {
                List noticeInstanceIds = ObjectDataUtils.getValue(data, ApprovalNoticeModel.NOTICE_IDS, List.class, Lists.newArrayList());
                smsInstanceIds.addAll(noticeInstanceIds);
            }
            if (EMAIL == notifyVia) {
                List noticeInstanceIds = ObjectDataUtils.getValue(data, ApprovalNoticeModel.NOTICE_IDS, List.class, Lists.newArrayList());
                emailInstanceIds.addAll(noticeInstanceIds);
            }
        }
        serviceFacade.bulkDeleteDirect(originalNoticeData, user);
        prmSmsAccess.deletedSmsConfigByIds(user, Lists.newArrayList(smsInstanceIds));
        prmEmailAccess.deletedEmailByIds(user, Lists.newArrayList(emailInstanceIds));
    }

    private void deleteExpireReminderPersonView(User user, String signSchemeId) {
        expireReminderPersonAccess.deleteItemsBySignSchemeId(user, signSchemeId);
    }

    private void deleteExpireReminderTypeView(User user, String signSchemeId) {
        expireReminderTypeAccess.deleteItemsBySignSchemeId(user, signSchemeId);
    }

    private void saveExpireReminderPerson(User user, String signSchemeId, PartnerChannelManage.ExpireReminderPersonView expireReminderPersonView) {
        expireReminderPersonView.getInternalPerson().forEach(p -> p.setIdentity(IdentityEnums.INTERNAL.getIdentity()));
        expireReminderPersonView.getExternalPerson().forEach(p -> p.setIdentity(IdentityEnums.EXTERNAL.getIdentity()));

        List<PartnerChannelManage.ReminderPerson> personList = Lists.newArrayList();
        personList.addAll(expireReminderPersonView.getInternalPerson());
        personList.addAll(expireReminderPersonView.getExternalPerson());
        List<ChannelManagementDTO.ReminderPerson> reminderPersonList = Lists.newArrayList();
        for (PartnerChannelManage.ReminderPerson reminderPerson : personList) {
            ChannelManagementDTO.ReminderPerson person = converter.convertDTO(reminderPerson, ChannelManagementDTO.ReminderPerson.class);
            person.setSignSchemeId(signSchemeId);
            reminderPersonList.add(person);
        }
        expireReminderPersonAccess.bulkSaveExpireReminderPerson(user, reminderPersonList);
    }

    private void saveExpireReminderType(User user, String signSchemeId, PartnerChannelManage.ExpireReminderTypeView expireReminderTypeView) {
        List<ChannelManagementDTO.ReminderType> reminderTypes = mergeExpireReminderType(signSchemeId, expireReminderTypeView.getAutoExpireReminderType(), expireReminderTypeView.getManualExpireReminderType());
        expireReminderTypeAccess.bulkSaveReminderTypes(user, reminderTypes);
    }

    private List<ChannelManagementDTO.ReminderType> mergeExpireReminderType(String signSchemeId, PartnerChannelManage.ExpireReminderType autoExpireReminderType, PartnerChannelManage.ExpireReminderType manualExpireReminderType) {
        return Stream.concat(
                assembleReminderTypeDTO(signSchemeId, autoExpireReminderType, ReminderTrigger.AUTO).stream(),
                assembleReminderTypeDTO(signSchemeId, manualExpireReminderType, ReminderTrigger.MANUAL).stream()
        ).collect(Collectors.toList());
    }

    private List<ChannelManagementDTO.ReminderType> assembleReminderTypeDTO(String signSchemeId, PartnerChannelManage.ExpireReminderType expireReminderType, ReminderTrigger reminderTrigger) {
        List<ChannelManagementDTO.ReminderType> reminderTypes = Lists.newArrayList();
        ChannelManagementDTO.ReminderType prmCrmReminder = converter.convertDTO(expireReminderType.getPrmCrmReminder(), ChannelManagementDTO.ReminderType.class);
        if (StringUtils.isBlank(prmCrmReminder.getReminderMethod())) {
            prmCrmReminder.setReminderMethod(TimeUnit.WEEK.getUnit());
        }
        if (prmCrmReminder.getReminderTime() == null) {
            prmCrmReminder.setReminderTime(1);
        }
        prmCrmReminder.setReminderTrigger(reminderTrigger.getTrigger());
        prmCrmReminder.setReminderMethod(NotifyViaEnums.PRM_CRM.getVia());
        reminderTypes.add(prmCrmReminder);

        ChannelManagementDTO.ReminderType smsReminder = converter.convertDTO(expireReminderType.getSmsReminder(), ChannelManagementDTO.ReminderType.class);
        if (StringUtils.isBlank(smsReminder.getReminderMethod())) {
            smsReminder.setReminderMethod(TimeUnit.WEEK.getUnit());
        }
        if (smsReminder.getReminderTime() == null) {
            smsReminder.setReminderTime(1);
        }
        smsReminder.setReminderTrigger(reminderTrigger.getTrigger());
        smsReminder.setReminderMethod(NotifyViaEnums.SMS.getVia());
        reminderTypes.add(smsReminder);

        ChannelManagementDTO.ReminderType emailReminder = converter.convertDTO(expireReminderType.getEmailReminder(), ChannelManagementDTO.ReminderType.class);
        if (StringUtils.isBlank(emailReminder.getReminderMethod())) {
            emailReminder.setReminderMethod(TimeUnit.WEEK.getUnit());
        }
        if (emailReminder.getReminderTime() == null) {
            emailReminder.setReminderTime(1);
        }
        emailReminder.setReminderTrigger(reminderTrigger.getTrigger());
        emailReminder.setReminderMethod(NotifyViaEnums.EMAIL.getVia());
        reminderTypes.add(emailReminder);

        ChannelManagementDTO.ReminderType crmReminder = converter.convertDTO(expireReminderType.getCrmReminder(), ChannelManagementDTO.ReminderType.class);
        if (StringUtils.isBlank(crmReminder.getReminderMethod())) {
            crmReminder.setReminderMethod(TimeUnit.WEEK.getUnit());
        }
        if (crmReminder.getReminderTime() == null) {
            crmReminder.setReminderTime(1);
        }
        crmReminder.setReminderTrigger(reminderTrigger.getTrigger());
        crmReminder.setReminderMethod(NotifyViaEnums.CRM.getVia());
        reminderTypes.add(crmReminder);

        ChannelManagementDTO.ReminderType prmAlertWindowReminder = converter.convertDTO(expireReminderType.getPrmAlertWindowReminder(), ChannelManagementDTO.ReminderType.class);
        prmAlertWindowReminder.setReminderTrigger(reminderTrigger.getTrigger());
        prmAlertWindowReminder.setReminderMethod(NotifyViaEnums.PRM_ALERT.getVia());
        if (StringUtils.isBlank(prmAlertWindowReminder.getReminderMethod())) {
            prmAlertWindowReminder.setReminderMethod(TimeUnit.DAY.getUnit());
        }
        if (prmAlertWindowReminder.getReminderTime() == null) {
            prmAlertWindowReminder.setReminderTime(3);
        }
        reminderTypes.add(prmAlertWindowReminder);
        //补充签约方案Id
        reminderTypes.forEach(reminderType -> reminderType.setSignSchemeId(signSchemeId));
        return reminderTypes;
    }

    private List<String> saveSignNotices(User user, PartnerChannelManage.SignScheme signScheme) {
        return saveNoticeData(user, signScheme);
    }

    private IObjectData saveSignScheme(User user, PartnerChannelManage.SignScheme signScheme) {
        ChannelManagementDTO.SignScheme convertSignScheme = converter.convertDTO(signScheme, ChannelManagementDTO.SignScheme.class);
        return signSchemeAccess.saveSignScheme(user, convertSignScheme);
    }

    private List<String> saveNoticeData(User user, PartnerChannelManage.SignScheme signScheme) {
        List<PartnerChannelManage.SignApprovalNotice> approvalNotices = signScheme.getApprovalNotices();
        if (CollectionUtils.isEmpty(approvalNotices)) {
            return signScheme.getNoticeIds();
        }
        return saveNoticeData(user, approvalNotices);
    }

    public List<String> saveNoticeData(User user, List<PartnerChannelManage.SignApprovalNotice> approvalNotices) {
        List<String> noticeIds = Lists.newArrayList();
        for (PartnerChannelManage.SignApprovalNotice approvalNotice : approvalNotices) {
            if (StringUtils.isNotBlank(approvalNotice.getApprovalNoticeId())) {
                noticeIds.add(approvalNotice.getApprovalNoticeId());
                continue;
            }
            BizScopeEnums bizScope = BizScopeEnums.fromString(approvalNotice.getBizScope());
            NotifyViaEnums notifyVia = NotifyViaEnums.of(approvalNotice.getNotifyVia());
            IObjectData noticeData = createDefaultApprovalNoticeData(user, bizScope, notifyVia, approvalNotice.getEnabled());
            noticeIds.add(noticeData.getId());
            if (NotifyViaEnums.SMS == notifyVia) {
                if (approvalNotice.getPassSms() == null && approvalNotice.getNonPassSms() == null) {
                    continue;
                }
                PartnerChannelManage.ApprovalNoticeSmsInstanceArg arg = new PartnerChannelManage.ApprovalNoticeSmsInstanceArg();
                arg.setApprovalNoticeId(noticeData.getId());
                arg.setBizScope(bizScope.getScope());
                arg.setReceiver(approvalNotice.getReceiver());
                arg.setPassSms(approvalNotice.getPassSms());
                arg.setNonPassSms(approvalNotice.getNonPassSms());
                arg.setAplApiName(approvalNotice.getAplApiName());
                saveSmsInstance(user, arg);
            } else if (NotifyViaEnums.EMAIL == notifyVia) {
                if (approvalNotice.getPassEmail() == null && approvalNotice.getNonPassEmail() == null) {
                    continue;
                }
                PartnerChannelManage.ApprovalNoticeEmailInstanceArg arg = new PartnerChannelManage.ApprovalNoticeEmailInstanceArg();
                arg.setApprovalNoticeId(noticeData.getId());
                arg.setBizScope(bizScope.getScope());
                arg.setReceiver(approvalNotice.getReceiver());
                arg.setSender(approvalNotice.getSender());
                arg.setPassEmail(approvalNotice.getPassEmail());
                arg.setNonPassEmail(approvalNotice.getNonPassEmail());
                arg.setAplApiName(approvalNotice.getAplApiName());
                saveEmailInstance(user, arg);
            }
        }
        return noticeIds;
    }

    public List<PartnerChannelManage.QualificationScheme> queryQualificationScheme(User user) {
        List<IObjectData> layoutSchemeList = layoutSchemeAccess.queryQualificationScheme(user);
        return getQualificationScheme(layoutSchemeList);
    }

    private List<PartnerChannelManage.QualificationScheme> getQualificationScheme(List<IObjectData> layoutSchemeList) {
        List<PartnerChannelManage.QualificationScheme> qualificationSchemeList = Lists.newArrayList();
        layoutSchemeList.forEach(data -> qualificationSchemeList.add(convertQualificationScheme(data)));
        return qualificationSchemeList.stream()
                .sorted(Comparator.comparingInt(PartnerChannelManage.QualificationScheme::getPriority))
                .collect(Collectors.toList());
    }

    private PartnerChannelManage.QualificationScheme convertQualificationScheme(IObjectData data) {
        String condition = data.get(LayoutSchemeModel.CONDITION, String.class);
        String aplApiName = data.get(LayoutSchemeModel.APL_API_NAME, String.class);
        String conditionType = getConditionType(condition);
        if (StringUtils.isNotBlank(aplApiName)) {
            conditionType = ConditionType.APL.getType();
        }
        String layoutApiName = data.get(LayoutSchemeModel.LAYOUT_API_NAME, String.class);
        String mappingRecordType = data.get(LayoutSchemeModel.MAPPING_RECORD_TYPE, String.class);
        Integer priority = data.get(LayoutSchemeModel.PRIORITY, Integer.class);
        return PartnerChannelManage.QualificationScheme.builder()
                .qualificationSchemeId(data.getId())
                .aplApiName(aplApiName)
                .condition(condition)
                .conditionType(conditionType)
                .layoutApiName(layoutApiName)
                .mappingRecordType(mappingRecordType)
                .priority(priority)
                .build();
    }

    public List<PartnerChannelManage.QualificationScheme> saveQualificationScheme(User user, List<PartnerChannelManage.QualificationScheme> qualificationScheme) {
        List<IObjectData> originalLayoutScheme = layoutSchemeAccess.queryQualificationScheme(user);
        serviceFacade.bulkDeleteDirect(originalLayoutScheme, user);
        Set<String> ruleCodes = originalLayoutScheme.stream().map(DBRecord::getId).collect(Collectors.toSet());
        paasRuleEngineService.deleteEngineRuleByRuleCodes(user, RuleEngineSceneEnums.CHANNEL_LAYOUT, SFAPreDefineObject.Partner.getApiName(), ruleCodes);
        List<IObjectData> savedLayoutScheme = layoutSchemeAccess.saveQualificationScheme(user, qualificationScheme);
        saveEngineRule(user, RuleEngineSceneEnums.CHANNEL_LAYOUT, SFAPreDefineObject.Partner.getApiName(), savedLayoutScheme);
        return getQualificationScheme(savedLayoutScheme);
    }

    public void delQualificationScheme(User user) {
        List<IObjectData> originalLayoutScheme = layoutSchemeAccess.queryQualificationScheme(user);
        serviceFacade.bulkDeleteDirect(originalLayoutScheme, user);
        Set<String> ruleCodes = originalLayoutScheme.stream().map(DBRecord::getId).collect(Collectors.toSet());
        paasRuleEngineService.deleteEngineRuleByRuleCodes(user, RuleEngineSceneEnums.CHANNEL_LAYOUT, SFAPreDefineObject.Partner.getApiName(), ruleCodes);
    }

    private void saveEngineRule(User user, RuleEngineSceneEnums scene, String objectApiName, List<IObjectData> savedScheme) {
        if (CollectionUtils.isEmpty(savedScheme)) {
            return;
        }
        savedScheme.forEach(ruleData -> {
            String condition = ruleData.get(LayoutSchemeModel.CONDITION, String.class);
            UseRangeFieldDataRender.UseRangeInfo useRangeInfo = parseConditionObject(condition);
            ConditionType conditionType = ConditionType.fromString(useRangeInfo.getType());
            if (ConditionType.CONDITION != conditionType) {
                return;
            }
            RuleGroupPojo ruleGroup = paasRuleEngineService.buildRuleGroup(user, scene, objectApiName, useRangeInfo.getValue(), ruleData.getId());
            boolean ruleSuccess = paasRuleEngineService.createRule(user, scene, ruleGroup);
            log.warn("saveEngineRule#ruleSuccess:{}", ruleSuccess);
        });
    }

    private boolean saveEngineRule(User user, RuleEngineSceneEnums scene, String objectApiName, String condition, String dataId) {
        if (StringUtils.isAnyBlank(objectApiName, condition)) {
            log.warn("saveEngineRule but param is blank, objectApiName:{}, condition:{}", objectApiName, condition);
            return true;
        }
        UseRangeFieldDataRender.UseRangeInfo useRangeInfo = parseConditionObject(condition);
        ConditionType conditionType = ConditionType.fromString(useRangeInfo.getType());
        if (ConditionType.CONDITION != conditionType) {
            return true;
        }
        RuleGroupPojo ruleGroup = paasRuleEngineService.buildRuleGroup(user, scene, objectApiName, useRangeInfo.getValue(), dataId);
        boolean ruleSuccess = paasRuleEngineService.createRule(user, scene, ruleGroup);
        log.warn("saveEngineRule#ruleSuccess:{}", ruleSuccess);
        return ruleSuccess;
    }

    public PartnerChannelManage.EnterpriseActivationSetting queryActivationSettingById(User user, String settingId) {
        IObjectData data = activationSettingAccess.queryActivationSettingById(user, settingId);
        return buildEnterpriseActivationSetting(data);
    }

    public PartnerChannelManage.SignScheme querySignSchemeById(User user, String signSchemeId) {
        IObjectData data = signSchemeAccess.querySignSchemeById(user, signSchemeId);
        if (data == null) {
            return null;
        }
        return assembleSignScheme(data, Lists.newArrayList(), Maps.newHashMap());
    }

    public List<PartnerChannelManage.Field> ableControlFields(User user) {
        List<PartnerChannelManage.Field> fields = Lists.newArrayList();
        IObjectDescribe objectDescribe;
        try {
            RequestContextManager.getContext().setAttribute(RequestContext.DIRECT_KEY, true);
            objectDescribe = describeLogicService.findObject(user.getTenantId(), SFAPreDefineObject.PartnerAgreementDetail.getApiName());
        } catch (Exception e) {
            log.warn("ableControlFields# can't find PartnerAgreement describe, tenant:{}", user.getTenantId(), e);
            return fields;
        }
        if (objectDescribe == null) {
            log.warn("ableControlFields# PartnerAgreement describe null, tenant:{}", user.getTenantId());
            return fields;
        }
        Optional.ofNullable(objectDescribe.getFieldDescribes()).orElse(Lists.newArrayList()).stream().filter(this::validField).forEach(field -> {
            PartnerChannelManage.Field item = PartnerChannelManage.Field.builder()
                    .apiName(field.getApiName())
                    .objectDescribeApiName(field.getDescribeApiName())
                    .label(field.getLabel())
                    .build();
            fields.add(item);
        });
        return fields;
    }

    public boolean validField(IFieldDescribe fieldDescribe) {
        if (fieldDescribe == null) {
            return false;
        }
        List<String> validPreFields = Lists.newArrayList(PartnerAgreementDetailModel.IDENTITY_CARDS, PartnerAgreementDetailModel.BUSINESS_LICENSE, PartnerAgreementDetailModel.AGREEMENT_ATTACHMENT);
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        boolean validPreField = fieldDescribeExt.isActive() && !fieldDescribeExt.isQuoteField() && validPreFields.contains(fieldDescribeExt.getApiName());
        return validPreField || fieldDescribeExt.isCustomField();
    }

    public void sortSignScheme(User user) {
        List<IObjectData> dataList = signSchemeAccess.queryAllSignScheme(user);
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        List<IObjectData> updateDataList = dataList.stream()
                .sorted(Comparator.comparingInt(this::getPriority))
                .collect(Collectors.toList());
        AtomicInteger priority = new AtomicInteger(1);
        updateDataList.forEach(data -> {
            data.set(SignSchemeModel.PRIORITY, priority.getAndIncrement());
        });
        signSchemeAccess.updateObjectsDataByFields(user, updateDataList, Sets.newHashSet(SignSchemeModel.PRIORITY));
    }

    private int getPriority(IObjectData data) {
        return ObjectDataUtils.getValue(data, SignSchemeModel.PRIORITY, Integer.class, 1);
    }

    public List<PartnerChannelManage.SignScheme> saveSignSchemeList(User user, PartnerChannelManage.SignSchemeListArg signSchemeArg) {
        Set<String> originalSignSchemeIds = bulkDeleteDirectSignScheme(user, signSchemeArg.getSignScheme());
        paasRuleEngineService.deleteEngineRuleByRuleCodes(user, RuleEngineSceneEnums.CHANNEL_AGREEMENT, SFAPreDefineObject.Partner.getApiName(), originalSignSchemeIds);
        for (PartnerChannelManage.SignScheme signScheme : signSchemeArg.getSignScheme()) {
            List<String> newNoticeIds = saveNoticeData(user, signScheme);
            signScheme.setNoticeIds(newNoticeIds);
        }
        List<IObjectData> dataList = signSchemeAccess.saveSignScheme(user, signSchemeArg.getSignScheme());
        saveEngineRule(user, RuleEngineSceneEnums.CHANNEL_AGREEMENT, SFAPreDefineObject.Partner.getApiName(), dataList);
        return buildSignSchemeList(user, dataList);
    }

    private Set<String> bulkDeleteDirectSignScheme(User user, List<PartnerChannelManage.SignScheme> signSchemeList) {
        List<IObjectData> originalSignSchemeData = signSchemeAccess.queryAllSignScheme(user);
        Set<String> updateSignSchemeIds = signSchemeList.stream().map(PartnerChannelManage.SignScheme::getSignSchemeId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<String> deletedNoticeIds = Lists.newArrayList();
        for (IObjectData data : originalSignSchemeData) {
            // 不在更新 id set 里的需要直接删除
            if (!updateSignSchemeIds.contains(data.getId())) {
                List<String> noticeIds = data.get(SignSchemeModel.NOTICE_IDS, List.class, Lists.newArrayList());
                deletedNoticeIds.addAll(noticeIds);
            }
        }
        Set<String> originalSignSchemeIds = originalSignSchemeData.stream().map(DBRecord::getId).collect(Collectors.toSet());
        serviceFacade.bulkDeleteDirect(originalSignSchemeData, user);
        List<IObjectData> originalNoticeData = approvalNoticeAccess.queryApprovalNoticeByIds(user, deletedNoticeIds);
        Set<String> deletedInstanceIds = getDeleteNoticeInstanceIds(originalNoticeData);
        serviceFacade.bulkDeleteDirect(originalNoticeData, user);

        // 删除短信
        prmSmsAccess.deletedSmsConfigByIds(user, Lists.newArrayList(deletedInstanceIds));
        return originalSignSchemeIds;
    }

    private Set<String> getDeleteNoticeInstanceIds(List<IObjectData> originalNoticeData) {
        if (CollectionUtils.isEmpty(originalNoticeData)) {
            return Sets.newHashSet();
        }
        Set<String> deletedInstanceIds = Sets.newHashSet();
        for (IObjectData data : originalNoticeData) {
            if (data.get(ApprovalNoticeModel.NOTICE_IDS) != null) {
                List itemList = data.get(ApprovalNoticeModel.NOTICE_IDS, List.class, Lists.newArrayList());
                deletedInstanceIds.addAll(itemList);
            }
        }
        return deletedInstanceIds;
    }

    public List<PartnerChannelManage.SignScheme> buildSignSchemeList(User user, List<IObjectData> data) {
        List<IObjectData> storedData = data.stream()
                .sorted(Comparator.comparingInt(signScheme -> signScheme.get(SignSchemeModel.PRIORITY, Integer.class, 0)))
                .collect(Collectors.toList());
        List<PartnerChannelManage.SignScheme> signSchemeList = Lists.newArrayList();
        List<PartnerChannelManage.ApprovalNotice> allNoticeData = getNoticeDataOriginal(user, storedData);
        Map<String, String> partnerAgreementNameMapping = getPartnerAgreementNameMappingOriginal(user, storedData);
        storedData.forEach(d -> signSchemeList.add(convertSignScheme(d, allNoticeData, partnerAgreementNameMapping)));
        return signSchemeList.stream()
                .sorted(Comparator.comparingInt(PartnerChannelManage.SignScheme::getPriority))
                .collect(Collectors.toList());
    }

    private Map<String, String> getPartnerAgreementNameMappingOriginal(User user, List<IObjectData> storedData) {
        if (CollectionUtils.isEmpty(storedData)) {
            return Maps.newHashMap();
        }
        Set<String> agreementIds = storedData.stream()
                .filter(d -> StringUtils.isNotBlank(ObjectDataUtils.getValueOrDefault(d, SignSchemeModel.AGREEMENT_ID, "")))
                .map(d -> d.get(SignSchemeModel.AGREEMENT_ID, String.class))
                .collect(Collectors.toSet());
        return getPartnerAgreementNameMappingByIds(user, agreementIds);
    }

    private List<PartnerChannelManage.ApprovalNotice> getNoticeDataOriginal(User user, List<IObjectData> storedData) {
        List<String> allNoticeIds = getNoticeIdsOriginal(storedData);
        List<IObjectData> data = approvalNoticeAccess.queryApprovalNoticeByIds(user, allNoticeIds);
        return data.stream().map(this::buildApprovalNotice).collect(Collectors.toList());
    }

    private List<String> getNoticeIdsOriginal(List<IObjectData> storedData) {
        Set<String> noticeSet = Sets.newHashSet();
        for (IObjectData data : storedData) {
            List<String> noticeIds = data.get(ApprovalNoticeModel.NOTICE_IDS, List.class, Lists.newArrayList());
            noticeSet.addAll(noticeIds);
        }
        return Lists.newArrayList(noticeSet);
    }

    private PartnerChannelManage.SignScheme convertSignScheme(@NotNull IObjectData d, List<PartnerChannelManage.ApprovalNotice> allNoticeData, Map<String, String> partnerAgreementNameMapping) {
        String agreementId = d.get(SignSchemeModel.AGREEMENT_ID, String.class);
        List<String> noticeIds = d.get(SignSchemeModel.NOTICE_IDS, List.class, Lists.newArrayList());
        String condition = d.get(SignSchemeModel.CONDITION, String.class);
        String conditionType = getConditionType(condition);
        String aplName = d.get(SignSchemeModel.APL_API_NAME, String.class);
        return PartnerChannelManage.SignScheme.builder()
                .signSchemeId(d.getId())
                .schemeName(d.get(SignSchemeModel.SCHEME_NAME, String.class))
                .agreementId(agreementId)
                .agreementName(partnerAgreementNameMapping.get(agreementId))//根据id查name
                .condition(condition)
                .conditionType(conditionType)
                .signMode(d.get(SignSchemeModel.SIGN_MODE, String.class))
                .aplApiName(aplName)
                .activateRoles(d.get(SignSchemeModel.ACTIVATE_ROLES, List.class, Lists.newArrayList()))
                .noticeIds(noticeIds)
                .approvalNoticeList(allNoticeData)
                .priority(d.get(SignSchemeModel.PRIORITY, Integer.class, 0))
                .build();
    }

    public PartnerChannelManage.CustomTextData saveCustomSignText(User user, PartnerChannelManage.CustomTextData customTextData) {
        return channelConfigAccess.saveCustomSignText(user, customTextData);
    }

    public PartnerChannelManage.CustomTextData fetchCustomSignText(User user) {
        return channelConfigAccess.fetchCustomSignText(user);
    }

    public PartnerChannelManage.CustomText saveRegisterCustomText(User user, PartnerChannelManage.CustomText customText) {
        channelConfigAccess.saveCustomSignText(user, REGISTER_CUSTOM_TEXT, customText);
        return customText;
    }

    public PartnerChannelManage.AdmissionConfig saveChannelAdmissionConfig(User user, PartnerChannelManage.AdmissionConfig admissionConfig) {
        List<String> changeFields = channelConfigAccess.saveChannelAdmissionConfig(user, admissionConfig);
        if (CollectionUtils.isEmpty(changeFields)) {
            return admissionConfig;
        }
        sendChangeAction(user);
        admissionConfigFields.stream().filter(changeFields::contains).findAny().ifPresent(admissionConfigField -> {
            resetAllModulesData(user);
        });
        return admissionConfig;
    }

    private void sendChangeAction(User user) {
        SwitchEventMessage switchEventMessage = SwitchEventMessage.builder()
                .tenantId(user.getTenantId())
                .operatorId(user.getUpstreamOwnerIdOrUserId())
                .language(I18NUtils.getLanguage())
                .build();
        log.warn("send OpenChannelAccessMQ tenant:{}, switchEventMessage:{}", user.getTenantId(), switchEventMessage);
        asyncTaskProducer.create(BizConfigKey.OPEN_CHANNEL_ACCESS.getKey(), JsonUtil.toJsonWithNullValues(switchEventMessage));
    }

    private void resetAllModulesData(User user) {
        log.warn("resetAllModulesData user:{}", user);
        // 删除注册审核
        delRegisterData(user);
        // 删除浏览规定
        delProvisionData(user);
        // 删除资质完善
        delQualificationData(user);
        // 删除协议签约
        delSignSchemeData(user);
    }

    private void delSignSchemeData(User user) {
        List<PartnerChannelManage.SimpleSignScheme> simpleSignSchemes = querySimpleSignSchemeList(user);
        if (CollectionUtils.isEmpty(simpleSignSchemes)) {
            return;
        }
        List<String> signSchemeIds = simpleSignSchemes.stream().map(PartnerChannelManage.SimpleSignScheme::getSignSchemeId).collect(Collectors.toList());
        signSchemeIds.forEach(signSchemeId -> {
            deleteSignScheme(user, signSchemeId);
        });
    }

    private void delQualificationData(User user) {
        delQualificationScheme(user);
    }

    private void delProvisionData(User user) {
        delProvisionScheme(user);
    }

    private void delRegisterData(User user) {
        delActivationSettings(user);
    }


    public PartnerChannelManage.AdmissionConfig fetchChannelAdmissionConfig(User user) {
        return channelConfigAccess.fetchChannelAdmissionConfig(user);
    }

    public PartnerChannelManage.SignScheme fetchSignScheme(User user, PartnerChannelManage.OpSignSchemeArg opSignSchemeArg) {
        IObjectData signSchemeData = signSchemeAccess.querySignSchemeById(user, opSignSchemeArg.getSignSchemeId());
        if (signSchemeData == null) {
            return null;
        }
        return assembleSignSchemeList(user, Lists.newArrayList(signSchemeData)).get(0);
    }

    public Boolean toggleSchemeStatus(User user, PartnerChannelManage.PushSignSchemeArg toggleSignSchemeArg) {
        IObjectData signSchemeData = signSchemeAccess.querySignSchemeById(user, toggleSignSchemeArg.getSignSchemeId());
        if (signSchemeData == null) {
            throw new ValidateException(I18N.text(PRM_CHANNEL_DATA_NOT_EXISTS, toggleSignSchemeArg.getSignSchemeId()));
        }
        Boolean originalPushed = ObjectDataUtils.getValue(signSchemeData, SignSchemeModel.PUSHED, Boolean.class, Boolean.FALSE);
        if (toggleSignSchemeArg.getPushed().equals(originalPushed)) {
            return Boolean.TRUE;
        }
//        channelCacheService.agreementDetailStatusChangeLock(user);
        try {
            signSchemeAccess.toggleSchemeStatus(user, signSchemeData, toggleSignSchemeArg.getPushed());
        } catch (Exception e) {
            log.error("toggleSchemeStatus error, tenant:{}, signSchemeId:{}, pushed:{}", user.getTenantId(), toggleSignSchemeArg.getSignSchemeId(), toggleSignSchemeArg.getPushed(), e);
//            channelCacheService.agreementDetailStatusChangeUnLock(user);
            return false;
        }
        boolean deleteSuccess = adjustEngineRules(user, toggleSignSchemeArg, signSchemeData);
        if (!deleteSuccess) {
            log.warn("toggleSchemeStatus Delete Failed");
//            channelCacheService.agreementDetailStatusChangeUnLock(user);
            signSchemeAccess.toggleSchemeStatus(user, signSchemeData, !toggleSignSchemeArg.getPushed());
            return false;
        }
        String operation = Boolean.TRUE.equals(toggleSignSchemeArg.getPushed()) ? "open" : "close";
        sendChangeAgreementDetailStatusMessage(user, toggleSignSchemeArg.getSignSchemeId(), operation);
        return true;
    }

    private void sendChangeAgreementDetailStatusMessage(User user, String signSchemeId, String operation) {
        PartnerChannelManage.ChangeStatusMessage statusMessage = PartnerChannelManage.ChangeStatusMessage.builder()
                .signSchemeId(signSchemeId)
                .tenantId(user.getTenantId())
                .operation(operation)
                .build();
        String messageBody = JSON.toJSONString(statusMessage);
        String messageKey = user.getTenantId()
                .concat("@")
                .concat("change_agreement_detail_status:")
                .concat(signSchemeId);
        log.warn("PartnerTaskService#sendSigningApprovalMsg by send rocketmq message {}", messageBody);
        asyncTaskProducer.create(CHANGE_AGREEMENT_DETAIL_STATUS, messageBody, messageKey);
    }

    private boolean adjustEngineRules(User user, PartnerChannelManage.PushSignSchemeArg toggleSignSchemeArg, IObjectData signSchemeData) {
        try {
            paasRuleEngineService.deleteEngineRuleByRuleCodes(user,
                    RuleEngineSceneEnums.CHANNEL_AGREEMENT,
                    SFAPreDefineObject.Partner.getApiName(),
                    Sets.newHashSet(toggleSignSchemeArg.getSignSchemeId()));
        } catch (Exception e) {
            log.warn("deleteEngineRuleByRuleCodes error, tenant:{}, signSchemeId:{}", user.getTenantId(), toggleSignSchemeArg.getSignSchemeId(), e);
            return false;
        }
        if (!Boolean.TRUE.equals(toggleSignSchemeArg.getPushed())) {
            return true;
        }
        String condition = ObjectDataUtils.getValue(signSchemeData, SignSchemeModel.CONDITION, String.class, null);
        if (StringUtils.isNotBlank(condition)) {
            return saveEngineRule(user, RuleEngineSceneEnums.CHANNEL_AGREEMENT, SFAPreDefineObject.Partner.getApiName(), condition, signSchemeData.getId());
        }
        return true;
    }

    public Boolean unLockToggleStatus(User user) {
        try {
//            channelCacheService.agreementDetailStatusChangeUnLock(user);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public ArrayList<PartnerChannelManage.AppConfigLayout> queryAppConfigLayout(User user) {
        PartnerChannelManage.AppConfigLayout channelMode = channelAppLayoutConfig.getChannelMode();
        PartnerChannelManage.AppConfigLayout applyToApp = channelAppLayoutConfig.getApplyToApp();
        PartnerChannelManage.AppConfigLayout relatedBusinessObject = channelAppLayoutConfig.getRelatedBusinessObject();
        return appLayoutI18nHandler(user, Lists.newArrayList(channelMode, applyToApp, relatedBusinessObject));
    }

    private ArrayList<PartnerChannelManage.AppConfigLayout> appLayoutI18nHandler(User user, ArrayList<PartnerChannelManage.AppConfigLayout> appConfigLayouts) {
        appConfigLayouts.forEach(appConfigLayout -> {
            String type = appConfigLayout.getType();
            if ("relatedBusinessObject".equals(type)) {
                Set<String> relatedBusinessObjects = Optional.ofNullable(appConfigLayout.getOptions()).orElse(Lists.newArrayList()).stream().map(o -> o.getValue()).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(relatedBusinessObjects)) {
                    return;
                }
                Map<String, IObjectDescribe> describeMap = describeEnhancer.fetchObjects(user, relatedBusinessObjects);
                appConfigLayout.getOptions().forEach(objOption -> {
                    String objectApiName = objOption.getValue();
                    IObjectDescribe describe = describeMap.get(objectApiName);
                    if (describe == null) {
                        return;
                    }
                    String displayName = describe.getDisplayName();
                    objOption.setLabel(displayName);
                });
                return;
            } else {
                // 其他
                if (StringUtils.isNotBlank(appConfigLayout.getI18nKey())) {
                    appConfigLayout.setI18nKey("-");
                    String text = I18N.text(appConfigLayout.getI18nKey());
                    if (StringUtils.isNotBlank(text)) {
                        appConfigLayout.setApiName(text);
                    } else {
                        log.warn("appLayoutI18nHandler but i18n value is empty, key:{}", appConfigLayout.getI18nKey());
                    }
                } else {
                    log.warn("appLayoutI18nHandler but i18n ket is empty, option:{}", appConfigLayout.getI18nKey());
                }
                Optional.ofNullable(appConfigLayout.getOptions()).orElse(Lists.newArrayList()).forEach(option -> {
                    String i18nKey = option.getI18nKey();
                    if (StringUtils.isBlank(i18nKey)) {
                        log.warn("appLayoutI18nHandler but i18n key is empty, option:{}", option.getValue());
                        return;
                    }
                    option.setI18nKey("-");
                    String text = I18N.text(i18nKey);
                    if (StringUtils.isNotBlank(text)) {
                        option.setLabel(text);
                    } else {
                        log.warn("appLayoutI18nHandler but i18n value is empty, i18nKey:{}", i18nKey);
                    }
                });
            }
        });
        return appConfigLayouts;
    }
}
